{% extends "base.html" %}

{% block title %}Support Tickets{% endblock %}

{% block content %}
<style>
    /* Main background color - Purple */
    body {
        background-color: #6b21a8;
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Dashboard card styling */
    .dashboard-card {
        background-color: #1e293b;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .dashboard-card-header {
        padding: 15px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dashboard-card-body {
        padding: 20px;
    }

    /* Button styling */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background-color: #e91e63;
        border-color: #e91e63;
    }

    .btn-primary:hover {
        background-color: #c2185b;
        border-color: #c2185b;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Form controls styling */
    .search-box, .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        padding: 10px 15px;
        width: 100%;
    }

    .search-box::placeholder, .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .search-box:focus, .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(233, 30, 99, 0.25);
        outline: none;
    }

    /* Select dropdown specific styling */
    .form-control option {
        background-color: #1a202c;
        color: white;
        padding: 8px 12px;
    }

    .form-control option:hover {
        background-color: #2d3748;
    }

    /* Table styling */
    .ticket-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        color: white;
        background-color: #1e293b;
    }

    .ticket-table th {
        background-color: #1a202c;
        color: white;
        border-bottom: 2px solid #4a5568;
        padding: 15px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }

    .ticket-table td {
        padding: 15px;
        border-bottom: 1px solid #4a5568;
        color: #ffffff;
        vertical-align: middle;
    }

    .ticket-table tr:hover {
        background-color: #3a4a5c;
    }

    .ticket-table tr:last-child td {
        border-bottom: none;
    }

    .ticket-link {
        color: #e91e63;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .ticket-link:hover {
        color: #f06292;
        text-decoration: underline;
    }

    /* Badge styling */
    .badge {
        padding: 6px 12px;
        font-weight: 500;
        border-radius: 20px;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        display: inline-block;
    }

    .badge-success {
        background-color: rgba(46, 204, 113, 0.2);
        border: 1px solid rgba(46, 204, 113, 0.3);
        color: #2ecc71;
    }

    .badge-info {
        background-color: rgba(52, 152, 219, 0.2);
        border: 1px solid rgba(52, 152, 219, 0.3);
        color: #3498db;
    }

    .badge-warning {
        background-color: rgba(241, 196, 15, 0.2);
        border: 1px solid rgba(241, 196, 15, 0.3);
        color: #f1c40f;
    }

    .badge-danger {
        background-color: rgba(231, 76, 60, 0.2);
        border: 1px solid rgba(231, 76, 60, 0.3);
        color: #e74c3c;
    }

    .badge-secondary {
        background-color: rgba(149, 165, 166, 0.2);
        border: 1px solid rgba(149, 165, 166, 0.3);
        color: #95a5a6;
    }

    .btn-view {
        background-color: rgba(52, 152, 219, 0.1);
        color: #3498db;
        border: 1px solid rgba(52, 152, 219, 0.3);
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        margin-right: 5px;
    }

    .btn-view:hover {
        background-color: rgba(52, 152, 219, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        color: #3498db;
        text-decoration: none;
    }

    .btn-view i {
        margin-right: 5px;
    }

    .btn-delete {
        background-color: rgba(231, 76, 60, 0.1);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.3);
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .btn-delete:hover {
        background-color: rgba(231, 76, 60, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        color: #e74c3c;
    }

    .btn-delete i {
        margin-right: 5px;
    }

    /* Bulk delete styling */
    .bulk-delete-container {
        background-color: rgba(231, 76, 60, 0.1);
        border: 1px solid rgba(231, 76, 60, 0.3);
        border-radius: 8px;
        padding: 10px 15px;
        margin-bottom: 15px;
        display: none;
        align-items: center;
        justify-content: space-between;
    }

    .bulk-delete-info {
        color: #e74c3c;
        font-weight: 500;
    }

    .btn-bulk-delete {
        background-color: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
        border: 1px solid rgba(231, 76, 60, 0.4);
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 0.8rem;
        transition: all 0.2s ease;
        cursor: pointer;
    }

    .btn-bulk-delete:hover {
        background-color: rgba(231, 76, 60, 0.3);
        transform: translateY(-1px);
    }

    /* Checkbox styling */
    .ticket-checkbox, .select-all-checkbox {
        width: 16px;
        height: 16px;
        accent-color: #e91e63;
        cursor: pointer;
    }

    .checkbox-column {
        width: 40px;
        text-align: center;
    }

    .empty-state {
        padding: 60px 20px;
        text-align: center;
        background-color: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        margin: 20px 0;
    }

    .empty-state i {
        font-size: 4rem;
        color: rgba(233, 30, 99, 0.3);
        margin-bottom: 20px;
    }

    .empty-state p {
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 25px;
        font-size: 1.1rem;
    }

    .last-responder {
        display: flex;
        flex-direction: column;
    }

    .responder-name {
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 3px;
    }

    .response-time {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
        display: flex;
        align-items: center;
    }

    .response-time:before {
        content: '\f017'; /* Font Awesome clock icon */
        font-family: 'Font Awesome 5 Free';
        margin-right: 5px;
        font-size: 0.7rem;
        opacity: 0.7;
    }

    .no-response {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
    }

    .no-response:before {
        content: '\f4ad'; /* Font Awesome comment-slash icon */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 5px;
        opacity: 0.7;
    }
</style>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 style="color: #ffffff;"><i class="fas fa-ticket-alt me-2" style="color: #e91e63;"></i>Support Tickets</h1>
            <p style="color: rgba(255, 255, 255, 0.7);">Manage your support requests and track their status</p>
        </div>
        <div class="col-md-4 text-md-end d-flex align-items-center justify-content-md-end">
            <a href="{{ url_for('ticket.create_ticket_page') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i>Create New Ticket
            </a>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="dashboard-card-header">
            <div class="row align-items-center w-100">
                {% if current_user.username == 'admintcg' %}
                <!-- Admin layout with 4 columns -->
                <div class="col-md-3">
                    <h5 class="mb-0" style="color: #ffffff; font-size: 1.1rem;">All Tickets</h5>
                </div>
                <div class="col-md-3">
                    <div class="position-relative">
                        <select id="usernameFilter" class="form-control">
                            <option value="">All Users</option>
                            {% set unique_usernames = tickets | map(attribute='username') | list | unique | sort %}
                            {% for username in unique_usernames %}
                            <option value="{{ username }}">{{ username }}</option>
                            {% endfor %}
                        </select>
                        <i class="fas fa-user" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.5); pointer-events: none;"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="position-relative">
                        <select id="statusFilter" class="form-control">
                            <option value="">All Statuses</option>
                            <option value="Open">Open</option>
                            <option value="In Progress">In Progress</option>
                            <option value="Resolved">Resolved</option>
                            <option value="Closed">Closed</option>
                        </select>
                        <i class="fas fa-flag" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.5); pointer-events: none;"></i>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="position-relative">
                        <input type="text" id="ticketSearch" class="search-box" placeholder="Search tickets...">
                        <i class="fas fa-search" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.5);"></i>
                    </div>
                </div>
                {% else %}
                <!-- Regular user layout with 3 columns -->
                <div class="col-md-4">
                    <h5 class="mb-0" style="color: #ffffff; font-size: 1.1rem;">Your Tickets</h5>
                </div>
                <div class="col-md-4">
                    <div class="position-relative">
                        <select id="usernameFilter" class="form-control">
                            <option value="">All Users</option>
                            {% set unique_usernames = tickets | map(attribute='username') | list | unique | sort %}
                            {% for username in unique_usernames %}
                            <option value="{{ username }}">{{ username }}</option>
                            {% endfor %}
                        </select>
                        <i class="fas fa-user" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.5); pointer-events: none;"></i>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="position-relative">
                        <input type="text" id="ticketSearch" class="search-box" placeholder="Search tickets...">
                        <i class="fas fa-search" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.5);"></i>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        <div class="dashboard-card-body p-0">
            {% if tickets %}
            {% if current_user.username == 'admintcg' %}
            <!-- Bulk delete container -->
            <div id="bulkDeleteContainer" class="bulk-delete-container">
                <span class="bulk-delete-info">
                    <span id="selectedCount">0</span> ticket(s) selected
                </span>
                <button id="bulkDeleteBtn" class="btn-bulk-delete">
                    <i class="fas fa-trash me-1"></i> Delete Selected
                </button>
            </div>
            {% endif %}
            <div class="table-responsive">
                <table class="ticket-table">
                    <thead>
                        <tr>
                            {% if current_user.username == 'admintcg' %}
                            <th class="checkbox-column">
                                <input type="checkbox" id="selectAll" class="select-all-checkbox" title="Select All">
                            </th>
                            {% endif %}
                            <th>Title</th>
                            <th>Username</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Last Response</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ticket in tickets %}
                        <tr class="ticket-row">
                            {% if current_user.username == 'admintcg' %}
                            <td class="checkbox-column">
                                <input type="checkbox" class="ticket-checkbox" value="{{ ticket._id }}" data-ticket-id="{{ ticket._id }}">
                            </td>
                            {% endif %}
                            <td>
                                <a href="{{ url_for('ticket.view_ticket', ticket_id=ticket._id) }}" class="ticket-link">
                                    {{ ticket.title }}
                                </a>
                            </td>
                            <td>
                                <span style="color: #ffffff; font-weight: 500;">{{ ticket.username }}</span>
                            </td>
                            <td>
                                {# Keep Critical for backward compatibility #}
                                {% set priority_class = {
                                    'Low': 'badge-success',
                                    'Medium': 'badge-info',
                                    'High': 'badge-warning',
                                    'Critical': 'badge-danger'
                                } %}
                                <span class="badge {{ priority_class.get(ticket.priority, 'badge-secondary') }}">
                                    {{ ticket.priority }}
                                </span>
                            </td>
                            <td>
                                {% set status = ticket.status|default('Open') %}
                                {% set status_class = {
                                    'Open': 'badge-info',
                                    'In Progress': 'badge-warning',
                                    'Resolved': 'badge-success',
                                    'Closed': 'badge-secondary'
                                } %}
                                <span class="badge {{ status_class.get(status, 'badge-secondary') }}">
                                    {{ status }}
                                </span>
                            </td>
                            <td>{{ ticket.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if ticket.responses and ticket.responses|length > 0 %}
                                    {% set last_response = ticket.responses[-1] %}
                                    <div class="last-responder">
                                        <span class="responder-name">{{ last_response.username }}</span>
                                        <small class="response-time">{{ last_response.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </div>
                                {% else %}
                                    <span class="no-response">No responses</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('ticket.view_ticket', ticket_id=ticket._id) }}" class="btn-view">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                                {% if current_user.username == 'admintcg' %}
                                <button class="btn-delete" onclick="deleteTicket('{{ ticket._id }}')">
                                    <i class="fas fa-trash me-1"></i> Delete
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <h4 class="text-white mb-3">No Support Tickets Found</h4>
                <p>You haven't created any support tickets yet.</p>
                <a href="{{ url_for('ticket.create_ticket_page') }}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i>Create Your First Ticket
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('ticketSearch');
        const usernameFilter = document.getElementById('usernameFilter');
        const statusFilter = document.getElementById('statusFilter');
        const ticketRows = document.querySelectorAll('.ticket-row');

        // Bulk delete elements
        const selectAllCheckbox = document.getElementById('selectAll');
        const ticketCheckboxes = document.querySelectorAll('.ticket-checkbox');
        const bulkDeleteContainer = document.getElementById('bulkDeleteContainer');
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        const selectedCountSpan = document.getElementById('selectedCount');

        function filterTickets() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedUsername = usernameFilter.value.toLowerCase();
            const selectedStatus = statusFilter ? statusFilter.value.toLowerCase() : '';

            ticketRows.forEach(row => {
                // Adjust column indices based on whether checkboxes are present
                const isAdmin = document.getElementById('selectAll') !== null;
                const titleColumnIndex = isAdmin ? 1 : 0;
                const usernameColumnIndex = isAdmin ? 2 : 1;
                const statusColumnIndex = isAdmin ? 4 : 3;

                const title = row.querySelector(`td:nth-child(${titleColumnIndex + 1})`).textContent.toLowerCase();
                const username = row.querySelector(`td:nth-child(${usernameColumnIndex + 1})`).textContent.toLowerCase();
                const status = row.querySelector(`td:nth-child(${statusColumnIndex + 1}) .badge`).textContent.toLowerCase();

                const matchesSearch = title.includes(searchTerm);
                const matchesUsername = selectedUsername === '' || username.includes(selectedUsername);
                const matchesStatus = selectedStatus === '' || status.includes(selectedStatus);

                if (matchesSearch && matchesUsername && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });

            // Update bulk delete visibility after filtering
            updateBulkDeleteVisibility();
        }

        // Bulk delete functionality
        function updateBulkDeleteVisibility() {
            if (!bulkDeleteContainer) return;

            const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
            const visibleCheckedBoxes = Array.from(checkedBoxes).filter(checkbox => {
                const row = checkbox.closest('.ticket-row');
                return row && row.style.display !== 'none';
            });

            if (visibleCheckedBoxes.length > 0) {
                bulkDeleteContainer.style.display = 'flex';
                selectedCountSpan.textContent = visibleCheckedBoxes.length;
            } else {
                bulkDeleteContainer.style.display = 'none';
            }
        }

        // Select all functionality
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const visibleCheckboxes = Array.from(ticketCheckboxes).filter(checkbox => {
                    const row = checkbox.closest('.ticket-row');
                    return row && row.style.display !== 'none';
                });

                visibleCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });

                updateBulkDeleteVisibility();
            });
        }

        // Individual checkbox functionality
        ticketCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBulkDeleteVisibility();

                // Update select all checkbox state
                if (selectAllCheckbox) {
                    const visibleCheckboxes = Array.from(ticketCheckboxes).filter(checkbox => {
                        const row = checkbox.closest('.ticket-row');
                        return row && row.style.display !== 'none';
                    });

                    const checkedVisibleBoxes = visibleCheckboxes.filter(cb => cb.checked);
                    selectAllCheckbox.checked = visibleCheckboxes.length > 0 && checkedVisibleBoxes.length === visibleCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedVisibleBoxes.length > 0 && checkedVisibleBoxes.length < visibleCheckboxes.length;
                }
            });
        });

        // Bulk delete button functionality
        if (bulkDeleteBtn) {
            bulkDeleteBtn.addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.ticket-checkbox:checked');
                const ticketIds = Array.from(checkedBoxes).map(checkbox => checkbox.value);

                if (ticketIds.length === 0) {
                    alert('No tickets selected');
                    return;
                }

                const confirmMessage = `Are you sure you want to delete ${ticketIds.length} ticket(s)? This action cannot be undone.`;
                if (confirm(confirmMessage)) {
                    bulkDeleteTickets(ticketIds);
                }
            });
        }

        searchInput.addEventListener('keyup', filterTickets);
        usernameFilter.addEventListener('change', filterTickets);
        if (statusFilter) {
            statusFilter.addEventListener('change', filterTickets);
        }
    });

    function deleteTicket(ticketId) {
        if (confirm('Are you sure you want to delete this ticket? This action cannot be undone.')) {
            fetch(`/tickets/${ticketId}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    alert('Ticket deleted successfully');
                    // Reload the page to refresh the ticket list
                    window.location.reload();
                } else {
                    alert('Error deleting ticket: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the ticket');
            });
        }
    }

    function bulkDeleteTickets(ticketIds) {
        // Show loading state
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        const originalText = bulkDeleteBtn.innerHTML;
        bulkDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Deleting...';
        bulkDeleteBtn.disabled = true;

        // Delete tickets sequentially to avoid overwhelming the server
        let deletedCount = 0;
        let failedCount = 0;

        const deletePromises = ticketIds.map(ticketId => {
            return fetch(`/tickets/${ticketId}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    deletedCount++;
                } else {
                    failedCount++;
                    console.error(`Failed to delete ticket ${ticketId}:`, data.message);
                }
            })
            .catch(error => {
                failedCount++;
                console.error(`Error deleting ticket ${ticketId}:`, error);
            });
        });

        Promise.all(deletePromises)
            .then(() => {
                // Show results
                let message = '';
                if (deletedCount > 0) {
                    message += `${deletedCount} ticket(s) deleted successfully`;
                }
                if (failedCount > 0) {
                    if (message) message += '. ';
                    message += `${failedCount} ticket(s) failed to delete`;
                }

                alert(message);

                // Reload the page to refresh the ticket list
                window.location.reload();
            })
            .finally(() => {
                // Restore button state
                bulkDeleteBtn.innerHTML = originalText;
                bulkDeleteBtn.disabled = false;
            });
    }
</script>
{% endblock %}
