{% extends "base.html" %}

{% block title %}Support Tickets{% endblock %}

{% block content %}
<style>
    /* Main background color - Purple */
    body {
        background-color: #6b21a8;
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Dashboard card styling */
    .dashboard-card {
        background-color: #1e293b;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.05);
    }

    .dashboard-card-header {
        padding: 15px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dashboard-card-body {
        padding: 20px;
    }

    /* Button styling */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background-color: #e91e63;
        border-color: #e91e63;
    }

    .btn-primary:hover {
        background-color: #c2185b;
        border-color: #c2185b;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Form controls styling */
    .search-box, .form-control {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        border-radius: 8px;
        padding: 10px 15px;
        width: 100%;
    }

    .search-box::placeholder, .form-control::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }

    .search-box:focus, .form-control:focus {
        background-color: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
        color: white;
        box-shadow: 0 0 0 0.25rem rgba(233, 30, 99, 0.25);
        outline: none;
    }

    /* Table styling */
    .ticket-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        color: white;
        background-color: #1e293b;
    }

    .ticket-table th {
        background-color: #1a202c;
        color: white;
        border-bottom: 2px solid #4a5568;
        padding: 15px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }

    .ticket-table td {
        padding: 15px;
        border-bottom: 1px solid #4a5568;
        color: #ffffff;
        vertical-align: middle;
    }

    .ticket-table tr:hover {
        background-color: #3a4a5c;
    }

    .ticket-table tr:last-child td {
        border-bottom: none;
    }

    .ticket-link {
        color: #e91e63;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .ticket-link:hover {
        color: #f06292;
        text-decoration: underline;
    }

    /* Badge styling */
    .badge {
        padding: 6px 12px;
        font-weight: 500;
        border-radius: 20px;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        display: inline-block;
    }

    .badge-success {
        background-color: rgba(46, 204, 113, 0.2);
        border: 1px solid rgba(46, 204, 113, 0.3);
        color: #2ecc71;
    }

    .badge-info {
        background-color: rgba(52, 152, 219, 0.2);
        border: 1px solid rgba(52, 152, 219, 0.3);
        color: #3498db;
    }

    .badge-warning {
        background-color: rgba(241, 196, 15, 0.2);
        border: 1px solid rgba(241, 196, 15, 0.3);
        color: #f1c40f;
    }

    .badge-danger {
        background-color: rgba(231, 76, 60, 0.2);
        border: 1px solid rgba(231, 76, 60, 0.3);
        color: #e74c3c;
    }

    .badge-secondary {
        background-color: rgba(149, 165, 166, 0.2);
        border: 1px solid rgba(149, 165, 166, 0.3);
        color: #95a5a6;
    }

    .btn-view {
        background-color: rgba(52, 152, 219, 0.1);
        color: #3498db;
        border: 1px solid rgba(52, 152, 219, 0.3);
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-view:hover {
        background-color: rgba(52, 152, 219, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-view i {
        margin-right: 5px;
    }

    .empty-state {
        padding: 60px 20px;
        text-align: center;
        background-color: rgba(255, 255, 255, 0.02);
        border-radius: 12px;
        margin: 20px 0;
    }

    .empty-state i {
        font-size: 4rem;
        color: rgba(233, 30, 99, 0.3);
        margin-bottom: 20px;
    }

    .empty-state p {
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 25px;
        font-size: 1.1rem;
    }

    .last-responder {
        display: flex;
        flex-direction: column;
    }

    .responder-name {
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 3px;
    }

    .response-time {
        color: rgba(255, 255, 255, 0.6);
        font-size: 0.75rem;
        display: flex;
        align-items: center;
    }

    .response-time:before {
        content: '\f017'; /* Font Awesome clock icon */
        font-family: 'Font Awesome 5 Free';
        margin-right: 5px;
        font-size: 0.7rem;
        opacity: 0.7;
    }

    .no-response {
        color: rgba(255, 255, 255, 0.5);
        font-style: italic;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
    }

    .no-response:before {
        content: '\f4ad'; /* Font Awesome comment-slash icon */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 5px;
        opacity: 0.7;
    }
</style>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 style="color: #ffffff;"><i class="fas fa-ticket-alt me-2" style="color: #e91e63;"></i>Support Tickets</h1>
            <p style="color: rgba(255, 255, 255, 0.7);">Manage your support requests and track their status</p>
        </div>
        <div class="col-md-4 text-md-end d-flex align-items-center justify-content-md-end">
            <a href="{{ url_for('ticket.create_ticket_page') }}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i>Create New Ticket
            </a>
        </div>
    </div>

    <div class="dashboard-card">
        <div class="dashboard-card-header">
            <div class="row align-items-center w-100">
                <div class="col-md-6">
                    <h5 class="mb-0" style="color: #ffffff; font-size: 1.1rem;">Your Tickets</h5>
                </div>
                <div class="col-md-6">
                    <div class="position-relative">
                        <input type="text" id="ticketSearch" class="search-box" placeholder="Search tickets...">
                        <i class="fas fa-search" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: rgba(255, 255, 255, 0.5);"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="dashboard-card-body p-0">
            {% if tickets %}
            <div class="table-responsive">
                <table class="ticket-table">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Username</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Last Response</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ticket in tickets %}
                        <tr class="ticket-row">
                            <td>
                                <a href="{{ url_for('ticket.view_ticket', ticket_id=ticket._id) }}" class="ticket-link">
                                    {{ ticket.title }}
                                </a>
                            </td>
                            <td>
                                <span style="color: #ffffff; font-weight: 500;">{{ ticket.username }}</span>
                            </td>
                            <td>
                                {# Keep Critical for backward compatibility #}
                                {% set priority_class = {
                                    'Low': 'badge-success',
                                    'Medium': 'badge-info',
                                    'High': 'badge-warning',
                                    'Critical': 'badge-danger'
                                } %}
                                <span class="badge {{ priority_class.get(ticket.priority, 'badge-secondary') }}">
                                    {{ ticket.priority }}
                                </span>
                            </td>
                            <td>
                                {% set status = ticket.status|default('Open') %}
                                {% set status_class = {
                                    'Open': 'badge-info',
                                    'In Progress': 'badge-warning',
                                    'Resolved': 'badge-success',
                                    'Closed': 'badge-secondary'
                                } %}
                                <span class="badge {{ status_class.get(status, 'badge-secondary') }}">
                                    {{ status }}
                                </span>
                            </td>
                            <td>{{ ticket.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if ticket.responses and ticket.responses|length > 0 %}
                                    {% set last_response = ticket.responses[-1] %}
                                    <div class="last-responder">
                                        <span class="responder-name">{{ last_response.username }}</span>
                                        <small class="response-time">{{ last_response.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </div>
                                {% else %}
                                    <span class="no-response">No responses</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('ticket.view_ticket', ticket_id=ticket._id) }}" class="btn-view">
                                    <i class="fas fa-eye me-1"></i> View
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <h4 class="text-white mb-3">No Support Tickets Found</h4>
                <p>You haven't created any support tickets yet.</p>
                <a href="{{ url_for('ticket.create_ticket_page') }}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-2"></i>Create Your First Ticket
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('ticketSearch');
        const ticketRows = document.querySelectorAll('.ticket-row');

        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();

            ticketRows.forEach(row => {
                const title = row.querySelector('td:first-child').textContent.toLowerCase();
                if (title.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    });
</script>
{% endblock %}
