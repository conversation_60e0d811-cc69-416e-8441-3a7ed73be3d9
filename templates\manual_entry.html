{% extends "base.html" %}
{% block title %}Manual Entry{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
</style>
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-4">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(26, 188, 156, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-keyboard fa-2x" style="color: #1abc9c;"></i>
                        </div>
                        <h1 class="mb-0 text-white">Manual Entry</h1>
                    </div>
                    <p class="text-white-50 mb-4">Manually add individual items to your inventory. Perfect for one-off additions or when you need precise control over item details.</p>
                    
                    <div class="alert alert-info" style="background-color: rgba(52, 152, 219, 0.2); border: 1px solid rgba(52, 152, 219, 0.3); color: #ffffff;">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Coming Soon
                        </h5>
                        <p class="mb-0">The manual entry functionality is currently under development. This page will soon include forms for adding individual inventory items with detailed specifications.</p>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-edit me-2" style="color: #2ecc71;"></i>
                                        Item Details
                                    </h5>
                                    <p class="card-text text-white-50">Add comprehensive item information including name, description, SKU, and category details.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-dollar-sign me-2" style="color: #f39c12;"></i>
                                        Pricing & Costs
                                    </h5>
                                    <p class="card-text text-white-50">Set purchase costs, selling prices, and profit margins for accurate inventory valuation.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-boxes me-2" style="color: #9b59b6;"></i>
                                        Quantity & Location
                                    </h5>
                                    <p class="card-text text-white-50">Specify quantities, storage locations, and condition details for proper inventory tracking.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-image me-2" style="color: #e74c3c;"></i>
                                        Images & Media
                                    </h5>
                                    <p class="card-text text-white-50">Upload product images and attach relevant media files for better inventory visualization.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="{{ url_for('inventory.inventory_dashboard') }}" class="btn" style="background-color: #6c757d; border-color: #6c757d; color: white;">
                            <i class="fas fa-arrow-left me-2"></i>Back to Inventory Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
