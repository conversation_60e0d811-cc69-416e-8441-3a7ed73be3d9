from flask import Blueprint, request, render_template, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from datetime import datetime, timezone
import requests
import logging
from bson import ObjectId

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Function to count open tickets for a user
def count_open_tickets(username=None, is_admin=False):
    """
    Count open tickets for a user or all tickets for an admin

    Args:
        username (str): Username to count tickets for
        is_admin (bool): Whether the user is an admin

    Returns:
        int: Number of open tickets
    """
    try:
        # Get MongoDB connection from current_app
        mongo_client = current_app.mongo_client
        db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
        tickets_collection = db['ticket']

        # Build query
        query = {'status': 'Open'}
        if not is_admin and username:
            query['username'] = username

        # Count tickets
        count = tickets_collection.count_documents(query)
        return count
    except Exception as e:
        logger.error(f"Error counting open tickets: {str(e)}")
        return 0

# Function to create tickets programmatically
def create_ticket_programmatically(title, description, priority, username, source=None):
    """
    Create a ticket programmatically from other parts of the application

    Args:
        title (str): Ticket title
        description (str): Ticket description
        priority (str): Ticket priority (Low, Medium, High)
        username (str): Username of the ticket creator
        source (str, optional): Source of the ticket (e.g., 'enterprise_request', 'buylist_rules')

    Returns:
        str: ID of the created ticket
    """
    try:
        # Get MongoDB connection from current_app
        mongo_client = current_app.mongo_client
        db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
        tickets_collection = db['ticket']

        # Use timezone-aware datetime
        current_time = datetime.now(timezone.utc)

        # Create ticket document
        ticket = {
            'title': title,
            'description': description,
            'priority': priority,
            'username': username,
            'status': 'Open',
            'created_at': current_time,
            'source': source
        }

        # Insert ticket into database
        result = tickets_collection.insert_one(ticket)
        ticket_id = str(result.inserted_id)
        logger.info(f"Ticket created programmatically with ID: {ticket_id}")

        return ticket_id
    except Exception as e:
        logger.error(f"Error creating ticket programmatically: {str(e)}")
        return None

ticket_bp = Blueprint('ticket', __name__)

@ticket_bp.route('/tickets')
@login_required
def tickets():
    # Get MongoDB connection from current_app
    mongo_client = current_app.mongo_client
    db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
    tickets_collection = db['ticket']

    # Allow all logged-in users to view their tickets
    if hasattr(current_user, 'roles') and ('Admin' in current_user.roles):
        # Admins can see all tickets
        tickets_data = list(tickets_collection.find())
        logger.info(f"Admin {current_user.username} viewing all tickets: {len(tickets_data)} found")
    else:
        # Regular users can only see their own tickets
        tickets_data = list(tickets_collection.find({'username': current_user.username}))
        logger.info(f"User {current_user.username} viewing their tickets: {len(tickets_data)} found")

    # Sort tickets by updated_at or created_at (most recent first)
    tickets_data.sort(key=lambda x: x.get('updated_at', x.get('created_at')), reverse=True)

    return render_template('tickets.html', tickets=tickets_data)

@ticket_bp.route('/tickets/create')
@login_required
def create_ticket_page():
    return render_template('create_ticket.html')

@ticket_bp.route('/tickets/create', methods=['POST'])
@login_required
def create_ticket():
    try:
        # Get MongoDB connection from current_app
        mongo_client = current_app.mongo_client
        db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
        tickets_collection = db['ticket']

        # Allow all logged-in users to create tickets
        data = request.get_json()

        # Validate the request data
        if not data:
            logger.error("Invalid request: No JSON data provided")
            return jsonify({'status': 'error', 'message': 'Invalid request format'}), 400

        title = data.get('title')
        description = data.get('description')
        priority = data.get('priority')

        # Validate required fields
        if not title or not description or not priority:
            logger.error("Invalid request: Missing required fields")
            return jsonify({'status': 'error', 'message': 'Missing required fields'}), 400

        # Validate priority value
        if priority not in ['Low', 'Medium', 'High']:
            logger.error(f"Invalid priority value: {priority}")
            return jsonify({'status': 'error', 'message': 'Invalid priority value'}), 400

        # Use timezone-aware datetime
        current_time = datetime.now(timezone.utc)

        ticket = {
            'title': title,
            'description': description,
            'priority': priority,
            'username': current_user.username,
            'status': 'Open',
            'created_at': current_time
        }
        result = tickets_collection.insert_one(ticket)
        logger.info(f"Ticket created with ID: {result.inserted_id}")
    except Exception as e:
        logger.error(f"Error creating ticket: {str(e)}")
        return jsonify({'status': 'error', 'message': f'Server error: {str(e)}'}), 500

    # Send email notification
    try:
        recipient_email = current_user.email
        if recipient_email:
            logger.info(f"Creating ticket for user {current_user.username} with email {recipient_email}")
            try:
                # Get Mailgun configuration from current_app
                mailgun_domain = current_app.config.get('MAILGUN_DOMAIN', 'tcgsync.com')
                mailgun_api_key = current_app.config.get('MAILGUN_API_KEY')

                # Send email via Mailgun API (EU region)
                email_response = requests.post(
                    f"https://api.eu.mailgun.net/v3/{mailgun_domain}/messages",
                    auth=("api", mailgun_api_key),
                    data={
                        "from": f"TCGSync Support <support@{mailgun_domain}>",
                        "to": [recipient_email],
                        "cc": ["<EMAIL>"],
                        "subject": "New Support Ticket Created",
                        "html": f"<html><body><h2>New Support Ticket Created</h2><p>Your ticket with title '{title}' has been created and is being reviewed by our team.</p><p>Priority: {priority}</p><p>We'll get back to you as soon as possible.</p></body></html>"
                    }
                )

                if email_response.status_code == 200:
                    logger.info(f"Email sent successfully to {recipient_email}")
                else:
                    logger.error(f"Failed to send email: {email_response.text}")
            except Exception as e:
                logger.error(f"Error sending email: {str(e)}")
        else:
            logger.warning("No email found for the current user.")
    except Exception as e:
        # Just log the error but don't fail the ticket creation
        logger.error(f"Error in email notification: {str(e)}")

    # Return success response
    return jsonify({'status': 'success', 'ticket_id': str(result.inserted_id)}), 201

@ticket_bp.route('/tickets/<ticket_id>', methods=['GET', 'POST'])
@login_required
def view_ticket(ticket_id):
    # Get MongoDB connection from current_app
    mongo_client = current_app.mongo_client
    db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
    tickets_collection = db['ticket']

    try:
        # Convert string ID to ObjectId
        object_id = ObjectId(ticket_id)
        ticket = tickets_collection.find_one({'_id': object_id})
    except Exception as e:
        logger.error(f"Error retrieving ticket {ticket_id}: {str(e)}")
        ticket = None

    if not ticket:
        flash('Ticket not found.', 'danger')
        return redirect(url_for('ticket.tickets'))

    # Check if user has permission to view this ticket
    if hasattr(current_user, 'roles') and 'Admin' in current_user.roles or current_user.username == ticket['username']:
        if request.method == 'POST':
            message = request.form['message']
            # Use timezone-aware datetime
            current_time = datetime.now(timezone.utc)

            response = {
                'ticket_id': ticket_id,
                'username': current_user.username,
                'message': message,
                'created_at': current_time
            }
            # Get MongoDB connection from current_app
            mongo_client = current_app.mongo_client
            db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
            tickets_collection = db['ticket']

            tickets_collection.update_one(
                {'_id': object_id},
                {'$push': {'responses': response}, '$set': {'updated_at': current_time, 'status': 'In Progress'}}
            )
            print(f"Response added to ticket ID: {ticket_id}")

            # Send email notification
            recipient_email = current_user.email
            if recipient_email:
                logger.info(f"Responding to ticket for user {current_user.username} with email {recipient_email}")
                try:
                    # Get Mailgun configuration from current_app
                    mailgun_domain = current_app.config.get('MAILGUN_DOMAIN', 'tcgsync.com')
                    mailgun_api_key = current_app.config.get('MAILGUN_API_KEY')

                    # Send email via Mailgun API (EU region)
                    email_response = requests.post(
                        f"https://api.eu.mailgun.net/v3/{mailgun_domain}/messages",
                        auth=("api", mailgun_api_key),
                        data={
                            "from": f"TCGSync Support <support@{mailgun_domain}>",
                            "to": [recipient_email],
                            "cc": ["<EMAIL>"],
                            "subject": f"Update on Your Support Ticket: {ticket['title']}",
                            "html": f"<html><body><h2>Support Ticket Update</h2><p>Your ticket '{ticket['title']}' has been updated with a new response.</p><p>Please log in to view the details.</p></body></html>"
                        }
                    )

                    if email_response.status_code == 200:
                        logger.info(f"Email sent successfully to {recipient_email}")
                    else:
                        logger.error(f"Failed to send email: {email_response.text}")
                except Exception as e:
                    logger.error(f"Error sending email: {str(e)}")
            else:
                logger.warning("No email found for the ticket owner.")
            flash('Response added successfully.')
            return redirect(url_for('ticket.view_ticket', ticket_id=ticket_id))

        # Get MongoDB connection from current_app
        mongo_client = current_app.mongo_client
        db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
        tickets_collection = db['ticket']

        responses = tickets_collection.find_one({'_id': object_id}, {'responses': 1}).get('responses', [])
        return render_template('view_ticket.html', ticket=ticket, responses=responses)
    else:
        flash('You do not have permission to view this ticket.')
        return redirect(url_for('ticket.tickets'))

@ticket_bp.route('/tickets/<ticket_id>/status', methods=['POST'])
@login_required
def update_ticket_status(ticket_id):
    # Get MongoDB connection from current_app
    mongo_client = current_app.mongo_client
    db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
    tickets_collection = db['ticket']

    try:
        # Convert string ID to ObjectId
        object_id = ObjectId(ticket_id)
        ticket = tickets_collection.find_one({'_id': object_id})

        if not ticket:
            return jsonify({'success': False, 'message': 'Ticket not found'}), 404

        # Check if ticket is already closed
        current_status = ticket.get('status', 'Open')
        if current_status == 'Closed':
            return jsonify({'success': False, 'message': 'Closed tickets cannot be reopened'}), 400

        # Check if user has permission to update this ticket
        if not (hasattr(current_user, 'roles') and 'Admin' in current_user.roles) and current_user.username != ticket['username']:
            return jsonify({'success': False, 'message': 'You do not have permission to update this ticket'}), 403

        # Get new status from request
        data = request.get_json()
        new_status = data.get('status')

        if not new_status or new_status not in ['Open', 'In Progress', 'Resolved', 'Closed']:
            return jsonify({'success': False, 'message': 'Invalid status'}), 400

        # Use timezone-aware datetime
        current_time = datetime.now(timezone.utc)

        # Update ticket status
        tickets_collection.update_one(
            {'_id': object_id},
            {'$set': {'status': new_status, 'updated_at': current_time}}
        )

        # Add a system response if status changed to Closed or Resolved
        if new_status in ['Closed', 'Resolved']:
            response = {
                'ticket_id': ticket_id,
                'username': current_user.username,
                'message': f"Ticket {new_status.lower()} by {current_user.username}",
                'created_at': current_time,
                'system': True
            }

            tickets_collection.update_one(
                {'_id': object_id},
                {'$push': {'responses': response}}
            )

        logger.info(f"Ticket {ticket_id} status updated to {new_status} by {current_user.username}")

        # Send email notification about status change
        try:
            # Get user email from the ticket
            user_data = db['users'].find_one({'username': ticket['username']})
            if user_data and 'email' in user_data:
                recipient_email = user_data['email']
                logger.info(f"Sending status update email to {recipient_email}")

                # Get Mailgun configuration from current_app
                mailgun_domain = current_app.config.get('MAILGUN_DOMAIN', 'tcgsync.com')
                mailgun_api_key = current_app.config.get('MAILGUN_API_KEY')

                # Send email via Mailgun API (EU region)
                email_response = requests.post(
                    f"https://api.eu.mailgun.net/v3/{mailgun_domain}/messages",
                    auth=("api", mailgun_api_key),
                    data={
                        "from": f"TCGSync Support <support@{mailgun_domain}>",
                        "to": [recipient_email],
                        "cc": ["<EMAIL>"],
                        "subject": f"Status Update: Your Support Ticket {ticket['title']}",
                        "html": f"<html><body><h2>Support Ticket Status Update</h2><p>Your ticket '{ticket['title']}' has been updated to <strong>{new_status}</strong>.</p><p>Please log in to view the details.</p></body></html>"
                    }
                )

                if email_response.status_code == 200:
                    logger.info(f"Status update email sent successfully to {recipient_email}")
                else:
                    logger.error(f"Failed to send status update email: {email_response.text}")
            else:
                logger.warning(f"No email found for user {ticket['username']}")
        except Exception as e:
            # Just log the error but don't fail the status update
            logger.error(f"Error sending status update email: {str(e)}")

        return jsonify({'success': True, 'message': f'Ticket status updated to {new_status}'})
    except Exception as e:
        logger.error(f"Error updating ticket status: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@ticket_bp.route('/tickets/<ticket_id>/delete', methods=['DELETE'])
@login_required
def delete_ticket(ticket_id):
    """Delete a ticket - only admins can delete tickets"""
    # Check if user is admin
    if not (hasattr(current_user, 'roles') and 'Admin' in current_user.roles) and current_user.username != 'admintcg':
        return jsonify({'success': False, 'message': 'You do not have permission to delete tickets'}), 403

    # Get MongoDB connection from current_app
    mongo_client = current_app.mongo_client
    db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
    tickets_collection = db['ticket']

    try:
        # Convert string ID to ObjectId
        object_id = ObjectId(ticket_id)
        ticket = tickets_collection.find_one({'_id': object_id})

        if not ticket:
            return jsonify({'success': False, 'message': 'Ticket not found'}), 404

        # Delete the ticket
        result = tickets_collection.delete_one({'_id': object_id})

        if result.deleted_count == 1:
            logger.info(f"Admin {current_user.username} deleted ticket {ticket_id}")
            return jsonify({'success': True, 'message': 'Ticket deleted successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to delete ticket'}), 500

    except Exception as e:
        logger.error(f"Error deleting ticket {ticket_id}: {str(e)}")
        return jsonify({'success': False, 'message': 'An error occurred while deleting the ticket'}), 500

@ticket_bp.route('/tickets/analyze-user', methods=['POST'])
@login_required
def analyze_user_tickets():
    """Analyze all tickets for a specific user using OpenAI"""
    try:
        data = request.get_json()
        username = data.get('username')

        if not username:
            return jsonify({'success': False, 'message': 'Username is required'}), 400

        # Get MongoDB connection from current_app
        mongo_client = current_app.mongo_client
        db = mongo_client[current_app.config.get('MONGO_DBNAME', 'test')]
        tickets_collection = db['ticket']

        # Get all tickets for the specified user
        user_tickets = list(tickets_collection.find({'username': username}))

        if not user_tickets:
            return jsonify({'success': False, 'message': f'No tickets found for user {username}'}), 404

        # Prepare ticket data for AI analysis
        tickets_summary = []
        for ticket in user_tickets:
            ticket_info = {
                'title': ticket.get('title', ''),
                'description': ticket.get('description', ''),
                'priority': ticket.get('priority', ''),
                'status': ticket.get('status', ''),
                'created_at': ticket.get('created_at', '').strftime('%Y-%m-%d %H:%M') if ticket.get('created_at') else '',
                'responses': []
            }

            # Add responses if they exist
            if 'responses' in ticket and ticket['responses']:
                for response in ticket['responses']:
                    if not response.get('system', False):  # Skip system messages
                        ticket_info['responses'].append({
                            'username': response.get('username', ''),
                            'message': response.get('message', ''),
                            'created_at': response.get('created_at', '').strftime('%Y-%m-%d %H:%M') if response.get('created_at') else ''
                        })

            tickets_summary.append(ticket_info)

        # Create AI prompt
        prompt = f"""You are an expert customer support analyst. Analyze the following support tickets for user '{username}' and provide a comprehensive summary.

TICKETS DATA:
"""

        for i, ticket in enumerate(tickets_summary, 1):
            prompt += f"""
TICKET #{i}:
Title: {ticket['title']}
Description: {ticket['description']}
Priority: {ticket['priority']}
Status: {ticket['status']}
Created: {ticket['created_at']}
"""
            if ticket['responses']:
                prompt += "Responses:\n"
                for response in ticket['responses']:
                    prompt += f"  - {response['username']} ({response['created_at']}): {response['message']}\n"
            prompt += "\n"

        prompt += f"""
ANALYSIS REQUIREMENTS:
Please provide a structured analysis in the following format:

📊 **SUMMARY OVERVIEW**
• Total tickets: {len(tickets_summary)}
• User: {username}
• Analysis date: {datetime.now().strftime('%Y-%m-%d')}

🔍 **ISSUE CATEGORIES & PATTERNS**
• Categorize the main types of issues reported
• Identify recurring problems or themes
• Note any escalation patterns

⚠️ **PRIORITY & URGENCY ANALYSIS**
• Breakdown of ticket priorities
• Most critical issues identified
• Response time patterns

📈 **STATUS & RESOLUTION TRACKING**
• Current status distribution
• Resolution success rate
• Outstanding issues requiring attention

🎯 **KEY INSIGHTS & RECOMMENDATIONS**
• Main pain points for this user
• Suggested improvements or solutions
• Proactive measures to prevent future issues

💡 **ACTION ITEMS CHECKLIST**
• Immediate actions needed
• Follow-up requirements
• Long-term solutions to implement

Keep the analysis concise but comprehensive. Use bullet points and clear formatting. Focus on actionable insights that will help improve customer support for this user."""

        # Initialize OpenAI client with API key (isolated from global config)
        try:
            from openai import OpenAI
            api_key = current_app.config.get('OPENAI_API_KEY')
            if not api_key:
                return jsonify({'success': False, 'message': 'OpenAI API key not configured'}), 500

            # Create client with minimal configuration to avoid proxy issues
            client = OpenAI(
                api_key=api_key,
                timeout=60.0,  # Set explicit timeout
                max_retries=2   # Set explicit retry count
            )

            # Make OpenAI API call using the new chat completions format
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert customer support analyst. Provide structured, actionable analysis of support tickets with clear formatting using bullet points and emojis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=1500,
                temperature=0.7
            )
        except ImportError as e:
            logger.error(f"OpenAI library import error: {str(e)}")
            return jsonify({'success': False, 'message': 'OpenAI library not available'}), 500
        except Exception as openai_error:
            logger.error(f"OpenAI API error: {str(openai_error)}")
            return jsonify({'success': False, 'message': f'OpenAI API error: {str(openai_error)}'}), 500

        analysis = response.choices[0].message.content.strip()

        logger.info(f"Successfully analyzed {len(tickets_summary)} tickets for user {username}")

        return jsonify({
            'success': True,
            'analysis': analysis,
            'ticket_count': len(tickets_summary),
            'username': username
        })

    except Exception as e:
        logger.error(f"Error analyzing tickets for user: {str(e)}")
        return jsonify({'success': False, 'message': f'An error occurred while analyzing tickets: {str(e)}'}), 500
