from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from pymongo import MongoClient, ASCENDING
from pymongo.errors import OperationFailure
import logging
from datetime import datetime
import os
import json
import re
import sys
from utils.shopify_utils import generate_json_files

# Add the parent directory to sys.path to import saautopricing
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from saautopricing import prepare_shopify_settings, PricingCalculator, fetch_pricing_data, get_exchange_rate, extract_condition, determine_printing_type
    SAAUTOPRICING_AVAILABLE = True
except ImportError:
    SAAUTOPRICING_AVAILABLE = False
    logger.warning("saautopricing module not available, prices will not be set automatically")

# Configure logging to show debug messages
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Export the blueprint creation function
__all__ = ['create_update_catalog_bp']

def create_update_catalog_bp(mongo_client):
    update_catalog_bp = Blueprint('update_catalog', __name__, url_prefix='/shopify/update-catalog')

    db = mongo_client['test']
    catalog_collection = db['catalog']
    shopify_collection = db['shProducts']

    # Create indexes for better performance
    try:
        catalog_collection.create_index([("gameName", ASCENDING)])
        catalog_collection.create_index([("expansionName", ASCENDING)])
        catalog_collection.create_index([("name", ASCENDING)])
        # Note: productId already has a unique index
        shopify_collection.create_index([("username", ASCENDING), ("productId", ASCENDING)])
    except OperationFailure as e:
        logger.warning(f"Some indexes already exist: {str(e)}")
    except Exception as e:
        logger.error(f"Error creating indexes: {str(e)}")

    @update_catalog_bp.route('/')
    @login_required
    def update_catalog():
        return render_template('shopify_update_catalog.html')

    @update_catalog_bp.route('/api/title-format', methods=['GET'])
    @login_required
    def get_title_format():
        try:
            settings = current_user.get_title_format()
            logger.info(f"Retrieved title format settings for user {current_user.username}: {settings}")
            return jsonify(settings)
        except Exception as e:
            logger.error(f"Error getting title format settings: {str(e)}")
            return jsonify({
                "includeName": True,
                "includeNumber": True,
                "includeExpansion": True,
                "includeAbbreviation": True,
                "order": ["name", "number", "expansion", "abbreviation"]
            })

    @update_catalog_bp.route('/api/title-format', methods=['POST'])
    @login_required
    def save_title_format():
        try:
            settings = request.json
            logger.info(f"Saving title format settings for user {current_user.username}: {settings}")
            if current_user.set_title_format(settings):
                return jsonify({"success": True, "message": "Title format settings saved successfully"})
            else:
                return jsonify({"success": False, "message": "Failed to save title format settings"})
        except Exception as e:
            logger.error(f"Error saving title format settings: {str(e)}")
            return jsonify({"success": False, "message": str(e)}), 500

    @update_catalog_bp.route('/api/conditions', methods=['GET'])
    @login_required
    def get_conditions():
        try:
            settings = current_user.get_shopify_update_catalog_settings()
            logger.info(f"Retrieved conditions settings for user {current_user.username}: {settings}")
            return jsonify(settings)
        except Exception as e:
            logger.error(f"Error getting conditions settings: {str(e)}")
            return jsonify({
                "selected_conditions": ["Near Mint", "Lightly Played", "Moderately Played", "Heavily Played", "Damaged"]
            })

    @update_catalog_bp.route('/api/conditions', methods=['POST'])
    @login_required
    def save_conditions():
        try:
            settings = request.json
            logger.info(f"Saving conditions settings for user {current_user.username}: {settings}")
            if current_user.set_shopify_update_catalog_settings(settings):
                return jsonify({"success": True, "message": "Conditions settings saved successfully"})
            else:
                return jsonify({"success": False, "message": "Failed to save conditions settings"})
        except Exception as e:
            logger.error(f"Error saving conditions settings: {str(e)}")
            return jsonify({"success": False, "message": str(e)}), 500

    @update_catalog_bp.route('/api/queued-files', methods=['GET'])
    @login_required
    def get_queued_files():
        root_dir = os.path.dirname(os.path.abspath(__file__))
        user_dir = os.path.join(root_dir, "..", "shopify_json_files", current_user.username)

        if not os.path.exists(user_dir):
            return jsonify({"files": []})

        files = []
        for filename in os.listdir(user_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(user_dir, filename)
                file_stats = os.stat(file_path)
                files.append({
                    "name": filename,
                    "size": file_stats.st_size,
                    "created": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified": datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                })

        # Sort files by modification time, newest first
        files.sort(key=lambda x: x['modified'], reverse=True)
        return jsonify({"files": files})

    @update_catalog_bp.route('/api/unmatched-items', methods=['GET'])
    @login_required
    def get_unmatched_items():
        game_name = request.args.get('gameName')
        expansion_name = request.args.get('expansionName')
        filter_option = request.args.get('filter')
        search_term = request.args.get('search', '').strip()
        page = int(request.args.get('page', 1))
        per_page = 10000  # Increased limit to show more results
        group_by_expansion = request.args.get('groupByExpansion', 'false').lower() == 'true'

        # Build the match conditions
        match_conditions = {}

        # Add search condition if provided
        if search_term:
            match_conditions["name"] = {"$regex": re.compile(search_term, re.IGNORECASE)}
        else:
            if game_name:
                match_conditions["gameName"] = game_name
            if expansion_name and expansion_name != "all":
                match_conditions["expansionName"] = expansion_name

        # Add filter conditions
        if filter_option == 'singles':
            match_conditions["isSingle"] = True
        elif filter_option == 'sealed':
            match_conditions["isSealed"] = True

        # Get the list of productIds that exist in shopify_collection for this user
        existing_product_ids = set(doc["productId"] for doc in shopify_collection.find(
            {"username": current_user.username},
            {"productId": 1, "_id": 0}
        ))

        # Exclude existing productIds
        match_conditions["productId"] = {"$nin": list(existing_product_ids)}

        # Count total documents for pagination
        total_count = catalog_collection.count_documents(match_conditions)
        total_pages = (total_count + per_page - 1) // per_page

        # Get unique expansion names for filters
        expansion_names = catalog_collection.distinct("expansionName", match_conditions)
        sorted_expansions = sorted([exp for exp in expansion_names if exp])

        # If grouping by expansion, return expansion stats instead of individual products
        if group_by_expansion:
            expansion_stats = []

            for expansion in sorted_expansions:
                # Count products in this expansion
                expansion_match = match_conditions.copy()
                expansion_match["expansionName"] = expansion
                count = catalog_collection.count_documents(expansion_match)

                if count > 0:
                    # Get the most recent release date for this expansion
                    release_date_pipeline = [
                        {"$match": expansion_match},
                        {"$sort": {"releasedOn": -1}},
                        {"$limit": 1},
                        {"$project": {"_id": 0, "releasedOn": 1}}
                    ]
                    release_date_result = list(catalog_collection.aggregate(release_date_pipeline))
                    release_date = release_date_result[0].get("releasedOn") if release_date_result else None

                    expansion_stats.append({
                        "expansionName": expansion,
                        "count": count,
                        "releaseDate": release_date
                    })

            # Sort expansions by release date (newest first)
            expansion_stats.sort(key=lambda x: x.get("releaseDate", "") or "", reverse=True)

            return jsonify({
                'count': len(expansion_stats),
                'total_count': total_count,
                'expansions': expansion_stats,
                'filters': {
                    "expansionNames": sorted_expansions
                }
            })
        else:
            # Get paginated results with only needed fields
            pipeline = [
                {"$match": match_conditions},
                {"$sort": {"name": 1}},
                {"$skip": (page - 1) * per_page},
                {"$limit": per_page},
                {"$project": {
                    "_id": 0,
                    "productId": 1,
                    "idProduct": 1,
                    "name": 1,
                    "expansionName": 1,
                    "number": 1,
                    "rarity": 1,
                    "isSingle": 1,
                    "isSealed": 1,
                    "image": 1,
                    "releasedOn": 1
                }}
            ]

            unmatched_products = list(catalog_collection.aggregate(pipeline))

            return jsonify({
                'count': len(unmatched_products),
                'total_count': total_count,
                'page': page,
                'total_pages': total_pages,
                'products': unmatched_products,
                'filters': {
                    "expansionNames": sorted_expansions
                }
            })

    @update_catalog_bp.route('/api/games', methods=['GET'])
    @login_required
    def get_games():
        games = catalog_collection.distinct('gameName')
        games = sorted([game for game in games if game and game.strip()])
        return jsonify(games)

    @update_catalog_bp.route('/api/expansions', methods=['GET'])
    @login_required
    def get_expansions():
        game_name = request.args.get('gameName')
        expansions = catalog_collection.distinct('expansionName', {'gameName': game_name})
        return jsonify(sorted([exp for exp in expansions if exp]))

    @update_catalog_bp.route('/api/expansion-items', methods=['GET'])
    @login_required
    def get_expansion_items():
        game_name = request.args.get('gameName')
        expansion_name = request.args.get('expansionName')

        if not game_name or not expansion_name:
            return jsonify({"error": "Game name and expansion name are required"}), 400

        # Build the match conditions
        match_conditions = {
            "gameName": game_name,
            "expansionName": expansion_name
        }

        # Get the list of productIds that exist in shopify_collection for this user
        existing_product_ids = set(doc["productId"] for doc in shopify_collection.find(
            {"username": current_user.username},
            {"productId": 1, "_id": 0}
        ))

        # Exclude existing productIds
        match_conditions["productId"] = {"$nin": list(existing_product_ids)}

        # Get all products for this expansion
        pipeline = [
            {"$match": match_conditions},
            {"$sort": {"name": 1}},
            {"$project": {
                "_id": 0,
                "productId": 1,
                "idProduct": 1,
                "name": 1,
                "expansionName": 1,
                "number": 1,
                "rarity": 1,
                "isSingle": 1,
                "isSealed": 1,
                "image": 1,
                "releasedOn": 1
            }}
        ]

        products = list(catalog_collection.aggregate(pipeline))

        return jsonify({
            'count': len(products),
            'products': products
        })

    @update_catalog_bp.route('/api/staged-items', methods=['GET'])
    @login_required
    def get_staged_items():
        try:
            # Get all productIds from user's shopify collection
            shopify_products = set(doc["productId"] for doc in shopify_collection.find(
                {"username": current_user.username},
                {"productId": 1, "_id": 0}
            ))

            # Get inventory items that aren't in shopify
            inventory_items = list(db['inventory'].find({
                "username": current_user.username,
                "productId": {"$nin": list(shopify_products)},
                "$or": [
                    {"shProduct": None},
                    {"matched_variant": False}
                ]
            }, {
                "_id": 0,
                "name": 1,
                "productId": 1,
                "condition": 1,
                "quantity": 1,
                "lowPrice": 1,
                "foil": 1,
                "expansionName": 1
            }))

            logger.info(f"Found {len(inventory_items)} inventory items for user {current_user.username}")
            return jsonify({"items": inventory_items})

        except Exception as e:
            logger.error(f"Error fetching staged items: {str(e)}")
            return jsonify({"error": str(e)}), 500

    @update_catalog_bp.route('/api/unique-languages', methods=['POST'])
    @login_required
    def get_unique_languages():
        try:
            data = request.json
            selected_product_ids = data.get('selectedProductIds', [])

            if not selected_product_ids:
                return jsonify({"message": "No products selected", "type": "error"}), 400

            # Query catalog collection for selected products
            catalog_query = {'productId': {'$in': selected_product_ids}}
            catalog_products = list(catalog_collection.find(catalog_query))

            # Extract unique languages from SKUs
            unique_languages = set()
            for product in catalog_products:
                for sku in product.get('skus', []):
                    if isinstance(sku, dict) and 'langAbbr' in sku:
                        unique_languages.add(sku['langAbbr'])

            logger.info(f"Found unique languages: {unique_languages}")
            return jsonify({"languages": sorted(list(unique_languages))})

        except Exception as e:
            logger.error(f"Error getting unique languages: {str(e)}")
            return jsonify({"message": str(e), "type": "error"}), 500

    @update_catalog_bp.route('/api/process', methods=['POST'])
    @login_required
    def process_game():
        data = request.json
        selected_product_ids = data.get('selectedProducts') or data.get('selectedProductIds', [])
        selected_conditions = data.get('selectedConditions', [])
        selected_languages = data.get('selectedLanguages', [])
        username = current_user.username

        logger.info(f"Number of selected products: {len(selected_product_ids)}")
        logger.info(f"Selected conditions: {selected_conditions}")

        if not selected_product_ids:
            logger.warning("No products selected for processing.")
            return jsonify({"message": "No products selected. Please select at least one product.", "type": "error"}), 400

        if not selected_conditions:
            logger.warning("No conditions selected for processing.")
            return jsonify({"message": "No conditions selected. Please select at least one condition.", "type": "error"}), 400

        # Save the selected conditions to the user profile
        try:
            settings = {"selected_conditions": selected_conditions}
            current_user.set_shopify_update_catalog_settings(settings)
            logger.info(f"Saved selected conditions to user profile: {selected_conditions}")
        except Exception as e:
            logger.error(f"Error saving conditions to user profile: {str(e)}")

        # Check if user is on a monthly, annual, or lifetime package
        subscription_name = current_user.get_subscription_name()
        logger.info(f"User {username} has subscription: {subscription_name}")

        # Apply limit only if the user is on a 'Free' subscription (case-insensitive)
        # Make sure we're not limiting users on Monthly Basic, Annual Basic, Monthly, Annual, or Lifetime packages
        is_limited_subscription = subscription_name and subscription_name.lower() == 'free'

        # Ensure Monthly Basic users aren't limited
        if subscription_name and ('monthly' in subscription_name.lower() or
                                 'annual' in subscription_name.lower() or
                                 'lifetime' in subscription_name.lower()):
            is_limited_subscription = False

        logger.info(f"User {username} has limited subscription: {is_limited_subscription}")

        if is_limited_subscription:
            if len(selected_product_ids) > 10:
                logger.warning(f"User {username} with Free subscription attempted to select {len(selected_product_ids)} items (limit is 10).")
                return jsonify({
                    "message": "Your current Free subscription only allows you to select up to 10 items at a time. Please upgrade to select more items.",
                    "type": "error"
                }), 403

        # Get catalog products first
        catalog_query = {'productId': {'$in': selected_product_ids}}
        catalog_products = list(catalog_collection.find(
            catalog_query,
            {
                '_id': 0,
                'productId': 1,
                'idProduct': 1,
                'name': 1,
                'expansionName': 1,
                'gameName': 1,
                'set_name': 1,
                'rarity': 1,
                'lang': 1,
                'type_line': 1,
                'oracle_text': 1,
                'abbreviation': 1,
                'language': 1,
                'metafieldGames': 1,
                'metafieldLegalities': 1,
                'extendedData': 1,
                'description': 1,
                'number': 1,
                'isSingle': 1,
                'isSealed': 1,
                'image': 1,
                'skus': 1,
                'upc': 1,  # Add UPC field
                'barcode': 1  # Add barcode field
            }
        ))

        # Create a map of existing catalog products by productId
        catalog_map = {str(p['productId']): p for p in catalog_products}

        # Get inventory items
        inventory_query = {
            'productId': {'$in': selected_product_ids},
            'username': current_user.username,
            '$or': [
                {'shProduct': None},
                {'matched_variant': False}
            ]
        }
        inventory_items = list(db['inventory'].find(inventory_query, {'_id': 0}))

        # Process inventory items
        for inventory_item in inventory_items:
            product_id = str(inventory_item['productId'])

            # Get or create the catalog entry
            if product_id in catalog_map:
                catalog_item = catalog_map[product_id]
            else:
                # Get base catalog data for this product
                catalog_item = catalog_collection.find_one(
                    {'productId': inventory_item['productId']},
                    {'_id': 0}
                )
                if catalog_item:
                    catalog_map[product_id] = catalog_item

            if catalog_item:
                # Ensure skus list exists
                if 'skus' not in catalog_item:
                    catalog_item['skus'] = []

                # Map condition from NM to Near Mint
                condition_map = {
                    'NM': 'Near Mint',
                    'LP': 'Lightly Played',
                    'MP': 'Moderately Played',
                    'HP': 'Heavily Played',
                    'DMG': 'Damaged'
                }

                # Create SKU for the inventory item
                sku = {
                    'langAbbr': 'EN',
                    'condName': condition_map.get(inventory_item.get('condition', 'NM'), 'Near Mint'),
                    'printingName': inventory_item.get('foil', 'Normal'),
                    'lowPrice': inventory_item.get('lowPrice', 0.00),
                    'skuId': inventory_item.get('skuId', str(inventory_item['productId']))
                }

                # Add SKU if it doesn't exist
                sku_exists = any(
                    existing_sku.get('skuId') == sku['skuId']
                    for existing_sku in catalog_item['skus']
                )
                if not sku_exists:
                    catalog_item['skus'].append(sku)

        # Get final list of products to process
        all_products = list(catalog_map.values())

        logger.info(f"Number of catalog products found: {len(catalog_products)}")
        logger.info(f"Number of inventory items found: {len(inventory_items)}")
        logger.info(f"Total products to process: {len(all_products)}")

        if not all_products:
            logger.info("No products found for the selected product IDs.")
            return jsonify({"message": "No products found for the given selection", "type": "error"}), 400

        try:
            for product in all_products:
                logger.info(f"Processing product: {product.get('productId')} - {product.get('name')}")

            # Define output directory
            root_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(root_dir, "..", "shopify_json_files", username)

            # Process the products and generate JSON files
            response = process_products(all_products, username, selected_conditions, output_dir, selected_languages)

            # Add file count to response
            if os.path.exists(output_dir):
                json_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]
                response["fileCount"] = len(json_files)
                response["message"] = f"Successfully created {len(json_files)} JSON files"
            return jsonify(response)
        except Exception as e:
            logger.error(f"Error in generate_json_files: {str(e)}")
            return jsonify({"message": f"An error occurred: {str(e)}", "type": "error"}), 500

    def get_condition_order(condition):
        order = {
            "Near Mint": 1,
            "Lightly Played": 2,
            "Moderately Played": 3,
            "Heavily Played": 4,
            "Damaged": 5
        }
        return order.get(condition, 6)  # Unknown conditions go at the end

    def construct_title(doc, title_format):
        logger.info(f"Starting title construction for product {doc.get('productId')} with format settings: {title_format}")

        # For sealed items, just use the name directly without any modifications
        if doc.get("isSealed"):
            raw_name = doc.get('name', 'No Title')
            logger.info(f"Using original name for sealed item: {raw_name}")
            return raw_name

        # For singles, apply the title formatting
        # Get raw values first
        raw_name = doc.get('name', 'No Title')
        raw_number = str(doc.get('number', ''))  # Convert to string and handle None case
        raw_abbreviation = doc.get('abbreviation')

        # No longer cleaning the name - use it as is
        logger.info(f"Raw values - name: {raw_name}, number: {raw_number}, abbreviation: {raw_abbreviation}")

        # Get expansion name
        expansion_name = doc.get('expansionName', '')

        # Format components
        components = {
            'name': raw_name,
            'number': f"({raw_number})" if raw_number else None,
            'abbreviation': f"({raw_abbreviation})" if raw_abbreviation else None,
            'expansion': f"({expansion_name})" if expansion_name else None
        }

        logger.info(f"Formatted components: {components}")

        # Build title based on format preferences
        title_parts = []

        # Check if title format has order and include settings
        if title_format and 'order' in title_format:
            # Use user-defined order and inclusion settings
            for part in title_format.get('order', ['name', 'number', 'expansion', 'abbreviation']):
                include_key = f'include{part.capitalize()}'
                if title_format.get(include_key, True) and components.get(part):
                    title_parts.append(components[part])
        else:
            # Default ordering
            # Always include name
            title_parts.append(components['name'])

            # Add number if it exists and includeNumber is true (default to true)
            if components['number'] and title_format.get('includeNumber', True):
                title_parts.append(components['number'])

            # Add expansion if it exists and includeExpansion is true (default to true)
            if components['expansion'] and title_format.get('includeExpansion', True):
                title_parts.append(components['expansion'])

            # Add abbreviation if it exists and includeAbbreviation is true (default to true)
            if components['abbreviation'] and title_format.get('includeAbbreviation', True):
                title_parts.append(components['abbreviation'])

        logger.info(f"Title parts: {title_parts}")

        final_title = ' '.join(title_parts)  # Join with space
        logger.info(f"Final constructed title: {final_title}")

        return final_title

    def save_json_file(products, output_dir, batch_number=1):
        """Save products to a JSON file with size limit of 10MB"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"products_batch{batch_number}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)

        # Convert to JSON string to check size
        json_data = json.dumps(products, ensure_ascii=False, indent=2)
        size_mb = len(json_data.encode('utf-8')) / (1024 * 1024)  # Size in MB

        logger.info(f"Saving JSON file: {filepath} with {len(products)} products (size: {size_mb:.2f}MB)")
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(json_data)
        logger.info(f"Successfully saved JSON file: {filepath}")

        return size_mb

    @update_catalog_bp.route('/api/smoshey-records', methods=['GET'])
    @login_required
    def get_smoshey_records():
        if current_user.username != "Xavier":
            return jsonify({"error": "Unauthorized"}), 403

        try:
            # Connect to smoshey database
            smoshey_db = mongo_client['smoshey']
            instock_collection = smoshey_db['inStock']

            # Get all productIds from user's shopify collection
            shopify_products = set(doc["productId"] for doc in shopify_collection.find(
                {"username": current_user.username},
                {"productId": 1, "_id": 0}
            ))

            # Get records from inStock collection that aren't in shopify
            missing_records = list(instock_collection.find(
                {
                    "productId": {"$nin": list(shopify_products)}
                },
                {
                    "_id": 0,
                    "productId": 1,
                    "Card Name": 1
                }
            ))

            return jsonify({
                'count': len(missing_records),
                'records': missing_records
            })

        except Exception as e:
            logger.error(f"Error fetching smoshey records: {str(e)}")
            return jsonify({"error": str(e)}), 500

    def process_products(catalog_products, username, selected_conditions, output_dir, selected_languages):
        products_by_expansion = {}
        title_format = current_user.get_title_format()
        logger.info(f"User {username} title format settings: {title_format}")

        # Initialize pricing calculator if saautopricing is available
        pricing_calculator = None
        user_currency = "USD"
        exchange_rate = 1.0
        tcgplayer_api_key = None

        if SAAUTOPRICING_AVAILABLE:
            try:
                # Get user profile for pricing settings
                user_profile = db['user'].find_one({'username': username})
                if user_profile:
                    # Get user currency and exchange rate
                    user_currency = user_profile.get('currency', 'USD')
                    exchange_rate = get_exchange_rate(user_currency)
                    logger.info(f"User currency: {user_currency}, Exchange rate: {exchange_rate}")

                    # Get pricing settings
                    settings = prepare_shopify_settings(user_profile)
                    pricing_calculator = PricingCalculator(settings, user_currency)
                    logger.info(f"Created pricing calculator with settings: {settings}")

                    # Get TCGPlayer API key
                    tcgplayer_key_doc = db['tcgplayerKey'].find_one({})
                    if tcgplayer_key_doc:
                        tcgplayer_api_key = tcgplayer_key_doc.get('latestKey')
                        logger.info("Retrieved TCGPlayer API key")
                    else:
                        logger.warning("TCGPlayer API key not found")
            except Exception as e:
                logger.error(f"Error initializing pricing calculator: {str(e)}")
                pricing_calculator = None

        for doc in catalog_products:
            logger.info(f"Processing product {doc.get('productId')} - {doc.get('name')}")
            logger.info(f"SKUs before processing: {doc.get('skus', [])}")

            expansion_name = doc.get("expansionName", "Unknown Expansion")
            if expansion_name not in products_by_expansion:
                products_by_expansion[expansion_name] = []

            # Initialize metafield_games and metafield_legalities before the if-else block
            metafield_games = doc.get("metafieldGames", [])
            metafield_legalities = doc.get("metafieldLegalities", {})

            # Generate tags
            if doc.get("isSealed"):
                # For sealed items, only include game, abbreviation, and expansion name
                # No need to include other tags for sealed items
                tags = [
                    doc.get("gameName", ""),
                    doc.get("abbreviation", ""),
                    doc.get("expansionName", "")
                ]
            else:
                # For singles, keep the original tag generation but exclude oracle text
                tags = [
                    doc.get("set_name", ""),
                    doc.get("rarity", ""),
                    doc.get("lang", ""),
                    doc.get("type_line", ""),
                    # Removed oracle_text as requested
                    doc.get("gameName", ""),
                    doc.get("expansionName", ""),
                    doc.get("abbreviation", ""),
                    doc.get("language", "")
                ]

                # Add metafieldGames as individual tags
                tags.extend(metafield_games)

                # Add metafieldLegalities as tags
                for format, legality in metafield_legalities.items():
                    if legality.lower() == "legal":
                        tags.append(format)

            # Build body HTML with table structure
            body_html = '<table class="singles-description-table" xmlns="http://www.w3.org/1999/html"><tbody>'

            # Get extended data
            extended_data = doc.get("extendedData", [])
            extended_data_dict = {data.get('displayName', ''): data.get('value', '') for data in extended_data}

            # Add extendedData items as tags (except Description) - only for singles
            if not doc.get("isSealed"):
                for data in extended_data:
                    name = data.get('name', '')
                    value = data.get('value', '')

                    display_name = data.get('displayName', '')
                    # Skip Description, card_text, and Oracle Text fields
                    if name == "Description" or name == "card_text" or name == "Oracle Text" or display_name == "Oracle Text":
                        continue

                    # Add value as a tag if it exists
                    if value:
                        tags.append(value)

            # Remove empty tags and join
            tags = [tag for tag in tags if tag and tag.strip()]
            tags_string = ', '.join(tags)

            # Add all extended data to table
            if extended_data:
                for data in extended_data:
                    display_name = data.get('displayName', '')
                    value = data.get('value', '')
                    if display_name and value and display_name not in ['Oracle Text', 'Reverse Oracle Text', 'Reverse Type']:
                        body_html += f"""
      <tr>
          <td>{display_name}:</td>
          <td>{value}</td>
      </tr>"""

            # Add basic details if not already included in extended data
            basic_details = [
                ('Set', doc.get('expansionName', '')),
                ('Rarity', doc.get('rarity', '')),
                ('Number', doc.get('number', '')),
                ('Language', doc.get('language', ''))
            ]

            for label, value in basic_details:
                if value and label not in [data.get('displayName') for data in extended_data]:
                    body_html += f"""
      <tr>
          <td>{label}:</td>
          <td>{value}</td>
      </tr>"""

            body_html += """
</tbody>
</table>"""

            # Oracle text section
            oracle_text = extended_data_dict.get('Oracle Text', doc.get('description', ''))
            if oracle_text:
                body_html += f"""
<div class="single-description-div">
        <div class="oracle-text">
            {oracle_text}
        </div>
</div>"""

            # Check for reverse/back face data
            reverse_type = extended_data_dict.get('Reverse Type', '')
            reverse_oracle = extended_data_dict.get('Reverse Oracle Text', '')

            if reverse_type or reverse_oracle:
                # Add reverse side table
                body_html += """
<table class="singles-reverse-description-table">
<tbody>"""
                if reverse_type:
                    body_html += f"""
      <tr>
          <td>Reverse Type:</td>
          <td>{reverse_type}</td>
      </tr>"""
                body_html += """
</tbody>
</table>"""

                # Add reverse oracle text if available
                if reverse_oracle:
                    body_html += f"""
<div class="single-description-div">
        <div class="reverseOracle-text">
            {reverse_oracle}
        </div>
</div>"""

            # Add metadata div
            product_id = doc.get("productId", "N/A")
            game_name = doc.get('gameName', '')
            game_name_to_cardtype = {
                'Magic: The Gathering': 'mtg',
                'Pokemon': 'pokemon',
                'Yu-Gi-Oh!': 'yugioh',
                'Akora TCG': 'akora',
                'One Piece Card Game': 'onepiece'
            }
            data_cardtype = game_name_to_cardtype.get(game_name, 'other')

            body_html += f'''
        <div class="catalogMetaData" style="visibility: hidden;"
             data-cardtype="{data_cardtype}"
             data-cardid="5"
             data-tcgid="{product_id}"
             data-lastupdated="{datetime.now().isoformat()}">
        </div>
        '''

            # Construct title using user's format settings
            title = construct_title(doc, title_format)

            variants = []
            logger.info(f"Processing variants for product {doc.get('productId')} - {doc.get('name')}")
            logger.info(f"Selected conditions: {selected_conditions}")
            logger.info(f"Selected languages: {selected_languages}")

            # Ensure skus list exists
            skus = doc.get("skus", [])
            if not isinstance(skus, list):
                skus = []
                logger.warning(f"SKUs for product {doc.get('productId')} was not a list. Defaulting to empty list.")

            # Handle sealed items differently
            if doc.get("isSealed"):
                logger.info(f"Processing sealed item: {doc.get('productId')} - {doc.get('name')}")
                # For sealed items, create a single variant without condition/language filtering

                # Use the product ID as SKU ID if no SKUs exist
                sku_id = doc.get("productId")
                if skus and isinstance(skus[0], dict):
                    sku_id = skus[0].get("skuId", sku_id)
                    low_price = skus[0].get("lowPrice", 0.00)
                else:
                    low_price = 0.00

                # Format price
                try:
                    price = "{:.2f}".format(float(low_price))
                except (ValueError, TypeError):
                    price = "0.00"

                logger.info(f"Creating sealed variant with price: {price}")
                variants.append({
                    "title": "Sealed",  # Simple title for sealed items
                    "price": price,
                    "sku": str(sku_id),
                    "barcode": str(sku_id),
                    "weight": 0.3,
                    "weightUnit": "GRAMS",
                    "options": ["Sealed"],
                    "requiresShipping": True,
                    "inventoryManagement": "SHOPIFY",
                    "inventoryPolicy": "DENY",
                    "condition_order": 1  # All sealed items get same condition order
                })
            else:
                # Process singles with condition filtering
                for sku in skus:
                    logger.info(f"Processing SKU: {sku}")
                    if not isinstance(sku, dict):
                        continue

                    # Map languageId to language abbreviation
                    language_map = {
                        1: "EN",
                        # Add other language mappings as needed
                    }
                    lang_abbr = language_map.get(sku.get("languageId"), sku.get("langAbbr", "EN"))

                    # Skip if languages are selected and this language isn't in the selection
                    if selected_languages and selected_languages != [] and lang_abbr not in selected_languages:
                        logger.info(f"Skipping language {lang_abbr} - not in selected languages")
                        continue

                    # Map conditionId to condition name
                    condition_id_map = {
                        1: "Near Mint",
                        2: "Lightly Played",
                        3: "Moderately Played",
                        4: "Heavily Played",
                        5: "Damaged"
                    }
                    condition = condition_id_map.get(sku.get('conditionId'), sku.get('condName', ''))
                    logger.info(f"Processing SKU condition: {condition}")

                    # Special handling for user Dezmu - filter conditions first
                    if username == "Dezmu":
                        if condition not in ["Near Mint", "Lightly Played"]:
                            logger.info(f"Skipping condition {condition} for user Dezmu")
                            continue
                    # Special handling for user Xavier
                    elif username == "Xavier":
                        if condition != "Near Mint":
                            logger.info(f"Skipping condition {condition} for user Xavier")
                            continue

                    # After user-specific filtering, check if condition is in selected conditions
                    if condition not in selected_conditions:
                        logger.info(f"Skipping condition {condition} - not in selected conditions")
                        continue

                    printing = sku.get('printingName', '')
                    # Format variant title like warehouse:
                    # If printing is Normal, just show condition
                    # If printing is special (Foil etc), show "condition - printing"
                    if printing.lower() == 'normal':
                        variant_title = condition
                    else:
                        variant_title = f"{condition} - {printing}"

                    # Calculate price using saautopricing if available
                    if pricing_calculator and tcgplayer_api_key:
                        try:
                            # Get product ID
                            product_id = str(doc.get('productId'))

                            # Fetch pricing data from TCGPlayer API if not already fetched
                            if not hasattr(process_products, 'pricing_data'):
                                process_products.pricing_data = {}

                            if product_id not in process_products.pricing_data:
                                product_pricing = fetch_pricing_data([product_id], tcgplayer_api_key)
                                process_products.pricing_data.update(product_pricing)

                            # Get pricing data for this product
                            product_pricing = process_products.pricing_data.get(product_id, [])

                            # Only include subtypes that have valid prices
                            valid_subtypes = [p.get('subTypeName') for p in product_pricing if p.get('subTypeName')]

                            # Determine printing type
                            printing_type = determine_printing_type(variant_title, valid_subtypes)

                            # Find the price data for this printing type
                            matched_price = None
                            for p in product_pricing:
                                if p.get('subTypeName', '').lower() == printing_type.lower():
                                    matched_price = p
                                    break

                            if matched_price:
                                # Extract pricing info for this printing type and convert from USD to user currency
                                pricing_info = {}

                                # Get market price
                                market_price = matched_price.get('marketPrice')
                                if market_price is not None:
                                    pricing_info['marketPrice'] = float(market_price) * exchange_rate

                                # Get low price
                                low_price = matched_price.get('lowPrice')
                                if low_price is not None:
                                    pricing_info['lowPrice'] = float(low_price) * exchange_rate

                                # Get mid price
                                mid_price = matched_price.get('midPrice')
                                if mid_price is not None:
                                    pricing_info['midPrice'] = float(mid_price) * exchange_rate

                                # Get high price
                                high_price = matched_price.get('highPrice')
                                if high_price is not None:
                                    pricing_info['highPrice'] = float(high_price) * exchange_rate

                                # Create sku_info for price calculation
                                sku_info = {
                                    'pricingInfo': pricing_info,
                                    'condName': condition,
                                    'printingName': printing_type,
                                    'skuId': sku.get('skuId'),
                                    'variantTitle': variant_title
                                }

                                # Calculate price
                                calculated_price, is_missing, price_history = pricing_calculator.calculate_final_price(sku_info, doc)

                                if not is_missing and calculated_price is not None:
                                    price = "{:.2f}".format(calculated_price)
                                    logger.info(f"Calculated price for {variant_title}: {price} (using saautopricing)")
                                else:
                                    # Fall back to low price if calculation fails
                                    low_price = sku.get("lowPrice")
                                    if low_price is None:
                                        price = "0.00"
                                    else:
                                        try:
                                            price = "{:.2f}".format(float(low_price))
                                        except (ValueError, TypeError):
                                            price = "0.00"
                                    logger.info(f"Using fallback price for {variant_title}: {price} (calculation failed)")
                            else:
                                # Fall back to low price if no matching price data
                                low_price = sku.get("lowPrice")
                                if low_price is None:
                                    price = "0.00" # Default before checking prices collection
                                else:
                                    try:
                                        price = "{:.2f}".format(float(low_price))
                                    except (ValueError, TypeError):
                                        price = "0.00" # Default before checking prices collection
                                logger.info(f"Using fallback price for {variant_title}: {price} (no matching price data)")
                        except Exception as e:
                            # Fall back to low price if an error occurs
                            logger.error(f"Error calculating price for {variant_title}: {str(e)}")
                            low_price = sku.get("lowPrice")
                            if low_price is None:
                                price = "0.00" # Default before checking prices collection
                            else:
                                try:
                                    price = "{:.2f}".format(float(low_price))
                                except (ValueError, TypeError):
                                    price = "0.00" # Default before checking prices collection
                            logger.info(f"Using fallback price for {variant_title}: {price} (error occurred)")
                    else:
                        # Use low price if saautopricing is not available
                        low_price = sku.get("lowPrice")
                        if low_price is None:
                            logger.warning(f"Low price is None in SKU for product {doc.get('productId')} - {doc.get('name')}. Will check prices collection.")
                            price = "0.00" # Default before checking prices collection
                        else:
                            try:
                                price = "{:.2f}".format(float(low_price))
                            except (ValueError, TypeError) as e:
                                logger.error(f"Error processing SKU price for product {doc.get('productId')} - {doc.get('name')}: {str(e)}. Will check prices collection.")
                                price = "0.00" # Default before checking prices collection

                    # Fallback: If price is still "0.00", try fetching from the 'prices' collection
                    if price == "0.00":
                        try:
                            product_id_int = int(doc.get('productId'))
                            prices_collection = db['prices']
                            price_doc = prices_collection.find_one({'productId': product_id_int})

                            if price_doc and 'low' in price_doc and price_doc['low'] is not None:
                                fallback_price_val = price_doc['low']
                                try:
                                    price = "{:.2f}".format(float(fallback_price_val))
                                    logger.info(f"Using fallback price from 'prices' collection for {variant_title}: {price}")
                                except (ValueError, TypeError):
                                    logger.warning(f"Invalid price format in 'prices' collection for productId {product_id_int}. Keeping price as 0.00.")
                                    price = "0.00"
                            else:
                                logger.warning(f"No valid price found in 'prices' collection for productId {product_id_int}. Keeping price as 0.00.")
                                price = "0.00"

                        except ValueError:
                             logger.error(f"Could not convert productId {doc.get('productId')} to int for prices collection lookup.")
                             price = "0.00"
                        except Exception as e_prices:
                            logger.error(f"Error querying 'prices' collection for productId {doc.get('productId')}: {str(e_prices)}")
                            price = "0.00"

                    # Extract UPC/barcode from top-level fields for barcode
                    upc_barcode = doc.get('upc', '') or doc.get('barcode', '')
                    if upc_barcode:
                        upc_barcode = str(upc_barcode)

                    # Use UPC as barcode if available, otherwise fall back to SKU ID
                    barcode_value = upc_barcode if upc_barcode else str(sku.get("skuId", ""))

                    logger.info(f"Creating variant with title: {variant_title}, final price: {price}, barcode: {barcode_value}")
                    variants.append({
                        "title": variant_title,
                        "price": price,
                        "sku": str(sku.get("skuId", "")),  # Keep SKU as skuId
                        "barcode": barcode_value,  # Use UPC if available, otherwise skuId
                        "weight": 0.3,
                        "weightUnit": "GRAMS",
                        "options": [variant_title],
                        "requiresShipping": True,
                        "inventoryManagement": "SHOPIFY",
                        "inventoryPolicy": "DENY",
                        "condition_order": get_condition_order(condition)
                    })

            # Skip products with no variants after filtering
            if not variants:
                logger.info(f"Skipping product {doc.get('productId')} - {doc.get('name')} - no valid variants after filtering")
                continue

            logger.info(f"Final variants count for product {doc.get('productId')}: {len(variants)}")

            # Sort variants by printing (Normal first, then Foil) and then by condition order
            variants.sort(key=lambda x: (
                0 if x['title'].startswith("Normal") else 1,
                x['condition_order']
            ))

            # Remove the temporary 'condition_order' key
            for variant in variants:
                del variant['condition_order']

            product_status = "DRAFT" if any(variant["price"] == "0.00" for variant in variants) else "ACTIVE"

            if doc.get('gameName') == "Magic: The Gathering":
                product_type = "MTG Single" if doc.get('isSingle') else "MTG Sealed"
            else:
                product_type = f"{doc.get('gameName', 'Unknown')} {'Single' if doc.get('isSingle') else 'Sealed'}"

            # Create metafields list starting with game
            # Ensure gameName is included in metafield_games
            game_name = doc.get("gameName", "")
            if game_name and game_name not in metafield_games:
                metafield_games.append(game_name)

            metafields = []  # Removed all metafields as requested

            product_data = {
                "input": {
                    "title": title,
                    "published": True,
                    "status": "ACTIVE",
                    "publishedAt": datetime.now().strftime("%Y-%m-%d"),
                    "tags": tags_string,
                    "bodyHtml": body_html,
                    "vendor": doc.get("gameName", "Unknown Vendor"),
                    "productType": product_type,
                    "variants": variants,
                    "options": ["Title"],
                    "metafields": metafields
                },
                "media": [
                    {
                        "originalSource": doc.get("image", "No Image"),
                        "alt": f"Image for {title} - {doc.get('gameName', 'Unknown Game')}",
                        "mediaContentType": "IMAGE"
                    }
                ]
            }

            logger.info(f"Adding product data to expansion {expansion_name}")
            products_by_expansion[expansion_name].append(product_data)

        # Combine all products into a single list
        all_products = []
        for expansion_products in products_by_expansion.values():
            all_products.extend(expansion_products)

        logger.info(f"Total products to save: {len(all_products)}")

        # Split into batches if needed to stay under 10MB limit
        MAX_SIZE_MB = 10
        current_batch = []
        batch_number = 1
        file_count = 0

        for product in all_products:
            current_batch.append(product)

            # Check if batch would exceed size limit
            test_json = json.dumps(current_batch, ensure_ascii=False, indent=2)
            current_size_mb = len(test_json.encode('utf-8')) / (1024 * 1024)

            # If adding this product would exceed the limit, save the batch (without this product)
            if current_size_mb > MAX_SIZE_MB:
                # Remove the last product that pushed it over the limit
                last_product = current_batch.pop()

                # Save current batch
                if current_batch:  # Only save if there's something to save
                    save_json_file(current_batch, output_dir, batch_number)
                    file_count += 1
                    batch_number += 1

                # Start a new batch with the product that was removed
                current_batch = [last_product]

        # Save any remaining products
        if current_batch:
            save_json_file(current_batch, output_dir, batch_number)
            file_count += 1

        logger.info(f"All products processed and saved to {file_count} JSON files.")
        return {"message": f"{file_count} JSON files generated successfully. Check the directory: {output_dir}", "type": "success"}

    return update_catalog_bp
