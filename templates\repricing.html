{% extends "base.html" %}
{% block title %}Repricing{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
</style>
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-4">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(230, 126, 34, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-tags fa-2x" style="color: #e67e22;"></i>
                        </div>
                        <h1 class="mb-0 text-white">Repricing</h1>
                    </div>
                    <p class="text-white-50 mb-4">Automatically update product prices based on market data. Optimize pricing strategies to maximize profitability and competitiveness.</p>
                    
                    <div class="alert alert-info" style="background-color: rgba(52, 152, 219, 0.2); border: 1px solid rgba(52, 152, 219, 0.3); color: #ffffff;">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Coming Soon
                        </h5>
                        <p class="mb-0">The repricing functionality is currently under development. This page will soon include advanced pricing tools and market data integration.</p>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-chart-line me-2" style="color: #2ecc71;"></i>
                                        Market Analysis
                                    </h5>
                                    <p class="card-text text-white-50">Analyze market trends and competitor pricing to optimize your product prices.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-robot me-2" style="color: #9b59b6;"></i>
                                        Automated Pricing
                                    </h5>
                                    <p class="card-text text-white-50">Set up automated pricing rules based on your business strategy and market conditions.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-history me-2" style="color: #f39c12;"></i>
                                        Price History
                                    </h5>
                                    <p class="card-text text-white-50">Track price changes and performance metrics to make informed pricing decisions.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card" style="background-color: rgba(30, 41, 59, 0.8); border: 1px solid rgba(255, 255, 255, 0.1);">
                                <div class="card-body">
                                    <h5 class="card-title text-white">
                                        <i class="fas fa-cogs me-2" style="color: #e74c3c;"></i>
                                        Pricing Rules
                                    </h5>
                                    <p class="card-text text-white-50">Configure custom pricing rules and strategies for different product categories.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="{{ url_for('inventory.inventory_dashboard') }}" class="btn" style="background-color: #6c757d; border-color: #6c757d; color: white;">
                            <i class="fas fa-arrow-left me-2"></i>Back to Inventory Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
