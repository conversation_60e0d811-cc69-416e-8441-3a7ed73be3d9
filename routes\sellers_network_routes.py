from flask import Blueprint, render_template
from flask_login import login_required

# Create the sellers network blueprint
sellers_network_bp = Blueprint('sellers_network', __name__)

@sellers_network_bp.route('/sellers_network_dashboard')
@login_required
def sellers_network_dashboard():
    """Render the sellers network dashboard page"""
    return render_template('sellers_network_dashboard.html')

@sellers_network_bp.route('/browse_network')
@login_required
def browse_network():
    """Browse inventory from other stores in the network"""
    return render_template('sellers_network_browse.html')

@sellers_network_bp.route('/my_orders')
@login_required
def my_orders():
    """View orders placed with other stores"""
    return render_template('sellers_network_my_orders.html')

@sellers_network_bp.route('/incoming_orders')
@login_required
def incoming_orders():
    """View orders from other stores"""
    return render_template('sellers_network_incoming_orders.html')

@sellers_network_bp.route('/network_settings')
@login_required
def network_settings():
    """Configure network settings"""
    return render_template('sellers_network_settings.html')

@sellers_network_bp.route('/network_analytics')
@login_required
def network_analytics():
    """View network analytics"""
    return render_template('sellers_network_analytics.html')

@sellers_network_bp.route('/partner_stores')
@login_required
def partner_stores():
    """Manage partner stores"""
    return render_template('sellers_network_partners.html')
