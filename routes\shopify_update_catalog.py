from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import OperationFailure
from bson import ObjectId
import logging
from datetime import datetime
import os
import json
import requests
import re
import sys
from utils.shopify_utils import generate_json_files
from threading import Lock
from datetime import timedelta
import os

# Add the parent directory to sys.path to import saautopricing
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    from saautopricing import prepare_shopify_settings, PricingCalculator, fetch_pricing_data, get_exchange_rate, extract_condition, determine_printing_type
    SAAUTOPRICING_AVAILABLE = True
except ImportError:
    SAAUTOPRICING_AVAILABLE = False
    logger.warning("saautopricing module not available, prices will not be set automatically")

# Configure logging to show debug messages
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Currency cache implementation for video games
class CurrencyCache:
    def __init__(self, ttl_hours=24):
        self.cache = {}
        self.ttl = timedelta(hours=ttl_hours)
        self.lock = Lock()
        self.last_cleanup = datetime.utcnow()

    def get(self, currency):
        with self.lock:
            if currency in self.cache:
                rate_data = self.cache[currency]
                if datetime.utcnow() - rate_data['timestamp'] < self.ttl:
                    return rate_data['rate']
                else:
                    del self.cache[currency]
            return None

    def set(self, currency, rate):
        with self.lock:
            self.cache[currency] = {
                'rate': rate,
                'timestamp': datetime.utcnow()
            }

# Initialize global currency cache
video_game_currency_cache = CurrencyCache()

def get_video_game_exchange_rate(target_currency):
    """Get exchange rate for video game prices with caching"""
    if target_currency == 'USD':
        return 1.0

    # Hard-coded exchange rates (USD to target currency) - Updated January 2025
    exchange_rates = {
        'GBP': 0.8194,    # USD to British Pound
        'EUR': 0.9762,    # USD to Euro
        'AUD': 1.6249,    # USD to Australian Dollar
        'NZD': 1.7973,    # USD to New Zealand Dollar
        'MXN': 20.7211,   # USD to Mexican Peso
        'CAD': 1.4417,    # USD to Canadian Dollar
    }

    rate = exchange_rates.get(target_currency, 1.0)
    if rate == 1.0 and target_currency != 'USD':
        logger.warning(f"No exchange rate found for {target_currency}, using 1.0")
    else:
        logger.debug(f"Using hard-coded exchange rate for {target_currency}: {rate}")

    return rate

def convert_video_game_prices_to_currency(game, target_currency):
    """Convert all price fields in a video game document to target currency"""
    if target_currency == 'USD':
        return  # No conversion needed

    # List of price fields to convert
    price_fields = [
        'new-price', 'cib-price', 'loose-price',
        'box-only-price', 'manual-only-price', 'graded-price'
    ]

    for field in price_fields:
        if field in game and game[field]:
            converted_price = convert_video_game_price(game[field], target_currency)
            game[field] = converted_price
            logger.debug(f"Converted {field} from {game.get(field)} to {converted_price}")

def convert_video_game_price(price_str, target_currency):
    """Convert a USD price string to target currency"""
    if not price_str or target_currency == 'USD':
        return price_str

    try:
        # Extract numeric value from price string (remove $ and commas)
        price_value = float(price_str.replace('$', '').replace(',', ''))
        exchange_rate = get_video_game_exchange_rate(target_currency)
        converted_value = round(price_value * exchange_rate, 2)

        # Format with appropriate currency symbol
        currency_symbols = {
            'EUR': '€',
            'GBP': '£',
            'CAD': 'C$',
            'AUD': 'A$',
            'NZD': 'NZ$',
            'MXN': 'MX$',
            'JPY': '¥',
            'USD': '$'
        }

        symbol = currency_symbols.get(target_currency, target_currency + ' ')
        if target_currency == 'JPY':
            # Japanese Yen doesn't use decimal places
            return f"{symbol}{int(converted_value)}"
        else:
            return f"{symbol}{converted_value:.2f}"

    except (ValueError, TypeError) as e:
        logger.warning(f"Error converting price '{price_str}' to {target_currency}: {str(e)}")
        return price_str

def get_user_currency(username):
    """Get user's preferred currency from database"""
    try:
        # Use the proper MongoDB connection from config
        from config.config import Config

        client = Config.get_mongo_client()
        db = client[Config.MONGO_DBNAME]
        user_collection = db['user']

        user_profile = user_collection.find_one({'username': username})
        if user_profile:
            currency = user_profile.get('currency', 'USD')
            logger.info(f"Retrieved currency for user {username}: {currency}")
            return currency
        else:
            logger.warning(f"User profile not found for {username}, defaulting to USD")
            return 'USD'
    except Exception as e:
        logger.error(f"Error getting user currency for {username}: {str(e)}")
        return 'USD'

# Export the blueprint creation function
__all__ = ['create_update_catalog_bp']

def create_update_catalog_bp(mongo_client):
    update_catalog_bp = Blueprint('update_catalog', __name__, url_prefix='/shopify/update-catalog')

    db = mongo_client['test']
    catalog_collection = db['catalog']
    shopify_collection = db['shProducts']

    # Create indexes for better performance
    try:
        catalog_collection.create_index([("gameName", ASCENDING)])
        catalog_collection.create_index([("expansionName", ASCENDING)])
        catalog_collection.create_index([("name", ASCENDING)])
        # Note: productId already has a unique index
        shopify_collection.create_index([("username", ASCENDING), ("productId", ASCENDING)])

        # Create optimized indexes for board games collection
        boardgames_collection = db['boardgames']
        boardgames_collection.create_index([("name", ASCENDING)])  # Primary search field
        boardgames_collection.create_index([("name", "text")])     # Text search index
        boardgames_collection.create_index([("year_published", ASCENDING)])
        boardgames_collection.create_index([("rank", ASCENDING)])
        boardgames_collection.create_index([("average", DESCENDING)])  # For sorting by rating
        # Compound index for search + sort performance
        boardgames_collection.create_index([("name", ASCENDING), ("year_published", DESCENDING)])

    except OperationFailure as e:
        logger.warning(f"Some indexes already exist: {str(e)}")
    except Exception as e:
        logger.error(f"Error creating indexes: {str(e)}")

    @update_catalog_bp.route('/')
    @login_required
    def update_catalog():
        return render_template('shopify_update_catalog.html')

    @update_catalog_bp.route('/api/title-format', methods=['GET'])
    @login_required
    def get_title_format():
        try:
            settings = current_user.get_title_format()
            logger.info(f"Retrieved title format settings for user {current_user.username}: {settings}")
            return jsonify(settings)
        except Exception as e:
            logger.error(f"Error getting title format settings: {str(e)}")
            return jsonify({
                "includeName": True,
                "includeNumber": True,
                "includeExpansion": True,
                "includeAbbreviation": True,
                "order": ["name", "number", "expansion", "abbreviation"]
            })

    @update_catalog_bp.route('/api/title-format', methods=['POST'])
    @login_required
    def save_title_format():
        try:
            settings = request.json
            logger.info(f"Saving title format settings for user {current_user.username}: {settings}")
            if current_user.set_title_format(settings):
                return jsonify({"success": True, "message": "Title format settings saved successfully"})
            else:
                return jsonify({"success": False, "message": "Failed to save title format settings"})
        except Exception as e:
            logger.error(f"Error saving title format settings: {str(e)}")
            return jsonify({"success": False, "message": str(e)}), 500
            
    @update_catalog_bp.route('/api/conditions', methods=['GET'])
    @login_required
    def get_conditions():
        try:
            settings = current_user.get_shopify_update_catalog_settings()
            logger.info(f"Retrieved conditions settings for user {current_user.username}: {settings}")
            return jsonify(settings)
        except Exception as e:
            logger.error(f"Error getting conditions settings: {str(e)}")
            return jsonify({
                "selected_conditions": ["Near Mint", "Lightly Played", "Moderately Played", "Heavily Played", "Damaged"]
            })
            
    @update_catalog_bp.route('/api/conditions', methods=['POST'])
    @login_required
    def save_conditions():
        try:
            settings = request.json
            logger.info(f"Saving conditions settings for user {current_user.username}: {settings}")
            if current_user.set_shopify_update_catalog_settings(settings):
                return jsonify({"success": True, "message": "Conditions settings saved successfully"})
            else:
                return jsonify({"success": False, "message": "Failed to save conditions settings"})
        except Exception as e:
            logger.error(f"Error saving conditions settings: {str(e)}")
            return jsonify({"success": False, "message": str(e)}), 500

    @update_catalog_bp.route('/api/queued-files', methods=['GET'])
    @login_required
    def get_queued_files():
        root_dir = os.path.dirname(os.path.abspath(__file__))
        user_dir = os.path.join(root_dir, "..", "shopify_json_files", current_user.username)
        
        if not os.path.exists(user_dir):
            return jsonify({"files": []})
            
        files = []
        for filename in os.listdir(user_dir):
            if filename.endswith('.json'):
                file_path = os.path.join(user_dir, filename)
                file_stats = os.stat(file_path)
                files.append({
                    "name": filename,
                    "size": file_stats.st_size,
                    "created": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                    "modified": datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                })
        
        # Sort files by modification time, newest first
        files.sort(key=lambda x: x['modified'], reverse=True)
        return jsonify({"files": files})

    @update_catalog_bp.route('/api/unmatched-items', methods=['GET'])
    @login_required
    def get_unmatched_items():
        game_name = request.args.get('gameName')
        expansion_name = request.args.get('expansionName')
        filter_option = request.args.get('filter')
        search_term = request.args.get('search', '').strip()
        page = int(request.args.get('page', 1))
        per_page = 10000  # Increased limit to show more results

        # Build the match conditions
        match_conditions = {}
        
        # Add search condition if provided
        if search_term:
            match_conditions["name"] = {"$regex": re.compile(search_term, re.IGNORECASE)}
        else:
            if game_name:
                match_conditions["gameName"] = game_name
            if expansion_name and expansion_name != "all":
                match_conditions["expansionName"] = expansion_name

        # Add filter conditions
        if filter_option == 'singles':
            match_conditions["isSingle"] = True
        elif filter_option == 'sealed':
            match_conditions["isSealed"] = True

        # Get the list of productIds that exist in shopify_collection for this user
        existing_product_ids = set(doc["productId"] for doc in shopify_collection.find(
            {"username": current_user.username},
            {"productId": 1, "_id": 0}
        ))

        # Exclude existing productIds
        match_conditions["productId"] = {"$nin": list(existing_product_ids)}

        # Count total documents for pagination
        total_count = catalog_collection.count_documents(match_conditions)
        total_pages = (total_count + per_page - 1) // per_page

        # Get paginated results with only needed fields
        pipeline = [
            {"$match": match_conditions},
            {"$sort": {"name": 1}},
            {"$skip": (page - 1) * per_page},
            {"$limit": per_page},
            {"$project": {
                "_id": 0,
                "productId": 1,
                "idProduct": 1,
                "name": 1,
                "expansionName": 1,
                "number": 1,
                "rarity": 1,
                "isSingle": 1,
                "isSealed": 1,
                "image": 1,
                "releasedOn": 1
            }}
        ]

        unmatched_products = list(catalog_collection.aggregate(pipeline))

        # Get unique expansion names for filters
        expansion_names = catalog_collection.distinct("expansionName", match_conditions)
        
        return jsonify({
            'count': len(unmatched_products),
            'total_count': total_count,
            'page': page,
            'total_pages': total_pages,
            'products': unmatched_products,
            'filters': {
                "expansionNames": sorted([exp for exp in expansion_names if exp])
            }
        })

    @update_catalog_bp.route('/api/games', methods=['GET'])
    @login_required
    def get_games():
        games = catalog_collection.distinct('gameName')
        games = sorted([game for game in games if game and game.strip()])
        return jsonify(games)

    @update_catalog_bp.route('/api/expansions', methods=['GET'])
    @login_required
    def get_expansions():
        game_name = request.args.get('gameName')
        expansions = catalog_collection.distinct('expansionName', {'gameName': game_name})
        return jsonify(sorted([exp for exp in expansions if exp]))

    @update_catalog_bp.route('/api/board-games/search', methods=['GET'])
    @login_required
    def search_board_games():
        try:
            search_query = request.args.get('query', '').strip()
            page = int(request.args.get('page', 1))
            per_page = 25

            if not search_query:
                return jsonify({
                    'games': [],
                    'total_count': 0,
                    'total_pages': 0,
                    'current_page': page
                })

            # Get MongoDB collection for board games
            boardgames_collection = db['boardgames']

            # Build optimized aggregation pipeline with facet for count and data in single query
            # Use text search for better performance when possible, fallback to regex
            search_stage = {}

            # Check if search query is a single word (better for text search)
            if ' ' not in search_query and len(search_query) > 2:
                # Use text search for single words (faster)
                search_stage = {
                    '$match': {
                        '$text': {'$search': search_query}
                    }
                }
            else:
                # Use regex for multi-word or short queries
                search_stage = {
                    '$match': {
                        'name': {'$regex': search_query, '$options': 'i'}
                    }
                }

            pipeline = [
                # Stage 1: Match documents with optimized search
                search_stage,
                # Stage 2: Use facet to get both count and paginated results in one query
                {
                    '$facet': {
                        # Get total count
                        'totalCount': [
                            {'$count': 'count'}
                        ],
                        # Get paginated results
                        'data': [
                            # Add text search score for text queries (helps with relevance sorting)
                            {
                                '$addFields': {
                                    'searchScore': {
                                        '$cond': {
                                            'if': {'$gt': [{'$meta': 'textScore'}, 0]},
                                            'then': {'$meta': 'textScore'},
                                            'else': 1
                                        }
                                    }
                                }
                            } if ' ' not in search_query and len(search_query) > 2 else {'$addFields': {'searchScore': 1}},
                            # Sort by relevance score first, then name for consistent ordering
                            {'$sort': {'searchScore': -1, 'name': 1}},
                            # Skip for pagination
                            {'$skip': (page - 1) * per_page},
                            # Limit results per page
                            {'$limit': per_page},
                            # Project only needed fields to reduce data transfer
                            {
                                '$project': {
                                    '_id': 1,
                                    'name': 1,
                                    'thumbnail_url': 1,
                                    'image_url': 1,
                                    # Add computed field for best image URL
                                    'best_image_url': {
                                        '$cond': {
                                            'if': {'$and': [
                                                {'$ne': ['$thumbnail_url', None]},
                                                {'$ne': ['$thumbnail_url', '']},
                                                {'$ne': ['$thumbnail_url', 'N/A']}
                                            ]},
                                            'then': '$thumbnail_url',
                                            'else': {
                                                '$cond': {
                                                    'if': {'$and': [
                                                        {'$ne': ['$image_url', None]},
                                                        {'$ne': ['$image_url', '']},
                                                        {'$ne': ['$image_url', 'N/A']}
                                                    ]},
                                                    'then': '$image_url',
                                                    'else': None
                                                }
                                            }
                                        }
                                    },
                                    'year_published': 1,
                                    'min_players': 1,
                                    'max_players': 1,
                                    'playing_time': 1,
                                    'publishers': {'$slice': ['$publishers', 3]},  # Limit to first 3 publishers
                                    'designers': {'$slice': ['$designers', 3]},   # Limit to first 3 designers
                                    'id': 1,  # BGG ID which can serve as UPC
                                    'average': 1,  # Include rating for potential future sorting
                                    'rank': 1      # Include rank for potential future sorting
                                }
                            }
                        ]
                    }
                }
            ]

            # Execute optimized pipeline with performance hints
            result = list(boardgames_collection.aggregate(
                pipeline,
                allowDiskUse=True,  # Allow disk usage for large datasets
                cursor={'batchSize': 100},  # Optimize cursor batch size
                maxTimeMS=30000  # Set maximum execution time (30 seconds)
            ))

            # Extract results from facet
            if result and len(result) > 0:
                facet_result = result[0]
                games = facet_result.get('data', [])
                total_count = facet_result.get('totalCount', [])
                total_count = total_count[0]['count'] if total_count else 0
            else:
                games = []
                total_count = 0

            total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 0

            logger.info(f"Board games search for '{search_query}' returned {len(games)} results out of {total_count} total matches")

            return jsonify({
                'games': games,
                'total_count': total_count,
                'total_pages': total_pages,
                'current_page': page
            })

        except Exception as e:
            logger.error(f"Error searching board games: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @update_catalog_bp.route('/api/video-games/platforms', methods=['GET'])
    @login_required
    def get_video_game_platforms():
        """Get all distinct platforms from video games collection"""
        try:
            videogames_collection = db['videogames']

            # Get distinct platforms, excluding empty/null values
            platforms = videogames_collection.distinct('console-name', {
                'console-name': {'$exists': True, '$ne': '', '$ne': None}
            })

            # Sort platforms alphabetically
            platforms = sorted([platform for platform in platforms if platform])

            logger.info(f"Retrieved {len(platforms)} distinct platforms")
            return jsonify({'platforms': platforms})

        except Exception as e:
            logger.error(f"Error fetching video game platforms: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @update_catalog_bp.route('/api/video-games/genres', methods=['GET'])
    @login_required
    def get_video_game_genres():
        """Get distinct genres for a specific platform"""
        try:
            platform = request.args.get('platform', '')
            videogames_collection = db['videogames']

            # Build query
            query = {'genre': {'$exists': True, '$ne': '', '$ne': None}}
            if platform:
                query['console-name'] = platform

            # Get distinct genres
            genres = videogames_collection.distinct('genre', query)

            # Sort genres alphabetically
            genres = sorted([genre for genre in genres if genre])

            logger.info(f"Retrieved {len(genres)} distinct genres for platform: {platform or 'all'}")
            return jsonify({'genres': genres})

        except Exception as e:
            logger.error(f"Error fetching video game genres: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @update_catalog_bp.route('/api/video-games/search', methods=['GET'])
    @login_required
    def search_video_games():
        try:
            search_query = request.args.get('query', '').strip()
            platform = request.args.get('platform', '').strip()
            genre = request.args.get('genre', '').strip()
            page = int(request.args.get('page', 1))
            per_page = 25

            # Build search conditions - allow search without text query if platform/genre filters are provided
            if not search_query and not platform and not genre:
                return jsonify({
                    'games': [],
                    'total_count': 0,
                    'total_pages': 0,
                    'current_page': page,
                    'message': 'Please provide search criteria'
                })

            # Get MongoDB collection for video games
            videogames_collection = db['videogames']

            # Build search conditions with filters
            search_conditions = {}

            # Add text search if provided
            if search_query:
                search_conditions['$or'] = [
                    {'product-name': {'$regex': search_query, '$options': 'i'}},
                    {'console-name': {'$regex': search_query, '$options': 'i'}},
                    {'genre': {'$regex': search_query, '$options': 'i'}},
                    {'developer': {'$regex': search_query, '$options': 'i'}},
                    {'publisher': {'$regex': search_query, '$options': 'i'}}
                ]

            # Add platform filter if provided
            if platform:
                if '$and' not in search_conditions:
                    search_conditions['$and'] = []
                search_conditions['$and'].append({'console-name': platform})

            # Add genre filter if provided
            if genre:
                if '$and' not in search_conditions:
                    search_conditions['$and'] = []
                search_conditions['$and'].append({'genre': genre})

            # If we only have filters without text search, create a simple match
            if not search_query and (platform or genre):
                search_conditions = {}
                if platform:
                    search_conditions['console-name'] = platform
                if genre:
                    search_conditions['genre'] = genre

            # Build aggregation pipeline for efficient search and pagination
            pipeline = [
                # Stage 1: Match documents
                {'$match': search_conditions},
                # Stage 2: Use facet to get both count and paginated results
                {
                    '$facet': {
                        # Get total count
                        'totalCount': [
                            {'$count': 'count'}
                        ],
                        # Get paginated results
                        'data': [
                            # Sort by name for consistent ordering
                            {'$sort': {'name': 1}},
                            # Skip for pagination
                            {'$skip': (page - 1) * per_page},
                            # Limit results per page
                            {'$limit': per_page},
                            # Project only needed fields based on actual structure
                            {
                                '$project': {
                                    '_id': 1,
                                    'product-name': 1,
                                    'console-name': 1,
                                    'genre': 1,
                                    'developer': 1,
                                    'publisher': 1,
                                    'image-url': 1,
                                    'release-date': 1,
                                    'loose-price': 1,
                                    'cib-price': 1,
                                    'new-price': 1,
                                    'upc': 1,
                                    'description': 1,
                                    'player-count': 1,
                                    'disc-count': 1
                                }
                            }
                        ]
                    }
                }
            ]

            # Execute pipeline
            result = list(videogames_collection.aggregate(
                pipeline,
                allowDiskUse=True,
                cursor={'batchSize': 100},
                maxTimeMS=30000
            ))

            # Extract results from facet
            if result and len(result) > 0:
                facet_result = result[0]
                games = facet_result.get('data', [])
                total_count = facet_result.get('totalCount', [])
                total_count = total_count[0]['count'] if total_count else 0
            else:
                games = []
                total_count = 0

            # Get user's currency for price conversion
            user_currency = get_user_currency(current_user.username)
            logger.info(f"Converting video game prices to {user_currency} for user {current_user.username}")

            # Process games to format data properly for frontend
            for game in games:
                # Map fields to expected frontend names
                game['name'] = game.get('product-name', 'Unknown Game')
                game['platform'] = game.get('console-name', 'Unknown')
                game['image_url'] = game.get('image-url', '')  # Note: field uses hyphen in database

                # Convert all price fields to user's currency
                price_fields = [
                    'loose-price', 'cib-price', 'new-price', 'graded-price',
                    'box-only-price', 'manual-only-price', 'bgs-10-price',
                    'retail-loose-buy', 'retail-loose-sell',
                    'retail-cib-buy', 'retail-cib-sell',
                    'retail-new-buy', 'retail-new-sell'
                ]

                for price_field in price_fields:
                    if game.get(price_field):
                        game[price_field] = convert_video_game_price(game[price_field], user_currency)

                # Format release date
                if game.get('release-date'):
                    try:
                        # Extract year from release date
                        release_date = game['release-date']
                        if isinstance(release_date, dict) and '$date' in release_date:
                            # Handle MongoDB date format
                            from datetime import datetime
                            date_obj = datetime.fromisoformat(release_date['$date'].replace('Z', '+00:00'))
                            game['release_year'] = date_obj.year
                        elif isinstance(release_date, str):
                            # Handle string date format
                            date_obj = datetime.fromisoformat(release_date.replace('Z', '+00:00'))
                            game['release_year'] = date_obj.year
                        else:
                            game['release_year'] = None
                    except:
                        game['release_year'] = None

                # Format pricing for display
                game['price_display'] = game.get('loose-price', 'N/A')

                # Clean up UPC if it exists
                if game.get('upc'):
                    upc = game['upc']
                    if isinstance(upc, dict) and '$numberLong' in upc:
                        game['upc_display'] = upc['$numberLong']
                    else:
                        game['upc_display'] = str(upc)
                else:
                    game['upc_display'] = 'N/A'

            total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 0

            # Build search description for logging
            search_parts = []
            if search_query:
                search_parts.append(f"query: '{search_query}'")
            if platform:
                search_parts.append(f"platform: '{platform}'")
            if genre:
                search_parts.append(f"genre: '{genre}'")
            search_description = ", ".join(search_parts) if search_parts else "no criteria"

            logger.info(f"Video games search with {search_description} returned {len(games)} results out of {total_count} total matches")

            return jsonify({
                'games': games,
                'total_count': total_count,
                'total_pages': total_pages,
                'current_page': page
            })

        except Exception as e:
            logger.error(f"Error searching video games: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @update_catalog_bp.route('/api/user-currency', methods=['GET'])
    @login_required
    def get_user_currency_endpoint():
        """Get the current user's currency preference"""
        try:
            user_currency = get_user_currency(current_user.username)
            return jsonify({'currency': user_currency})
        except Exception as e:
            logger.error(f"Error getting user currency: {str(e)}")
            return jsonify({'currency': 'USD'})

    @update_catalog_bp.route('/api/board-games/push-to-shopify', methods=['POST'])
    @login_required
    def push_board_games_to_shopify():
        try:
            data = request.get_json()
            selected_game_ids = data.get('gameIds', [])
            price = data.get('price', 0.00)
            quantity = data.get('quantity', 1)

            if not selected_game_ids:
                return jsonify({'error': 'No games selected'}), 400

            # Validate price and quantity
            try:
                price = float(price)
                quantity = int(quantity)
                if price < 0 or quantity < 0:
                    return jsonify({'error': 'Price and quantity must be non-negative'}), 400
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid price or quantity format'}), 400

            # Get current user
            username = current_user.username if current_user else 'unknown'

            # Get user's Shopify credentials
            user_collection = db['user']
            user_data = user_collection.find_one({'username': username})

            if not user_data:
                return jsonify({'error': 'User not found'}), 404

            shopify_access_token = user_data.get('shopifyAccessToken')
            shopify_store_name = user_data.get('shopifyStoreName')

            if not shopify_access_token or not shopify_store_name:
                return jsonify({'error': 'Shopify credentials not configured. Please set up your Shopify integration first.'}), 400

            # Get MongoDB collection for board games
            boardgames_collection = db['boardgames']

            # Convert string IDs to ObjectIds if needed
            object_ids = []
            for game_id in selected_game_ids:
                try:
                    if isinstance(game_id, str) and len(game_id) == 24:
                        object_ids.append(ObjectId(game_id))
                    else:
                        object_ids.append(game_id)
                except:
                    object_ids.append(game_id)

            # Fetch selected board games
            selected_games = list(boardgames_collection.find({'_id': {'$in': object_ids}}))

            if not selected_games:
                return jsonify({'error': 'No games found with provided IDs'}), 404

            # Push games to Shopify
            response = push_board_games_to_shopify_store(selected_games, shopify_store_name, shopify_access_token, username, price, quantity)

            logger.info(f"Pushed {len(selected_games)} board games to Shopify for user {username}")

            return jsonify(response)

        except Exception as e:
            logger.error(f"Error pushing board games to Shopify: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @update_catalog_bp.route('/api/staged-items', methods=['GET'])
    @login_required
    def get_staged_items():
        try:
            # Get all productIds from user's shopify collection
            shopify_products = set(doc["productId"] for doc in shopify_collection.find(
                {"username": current_user.username},
                {"productId": 1, "_id": 0}
            ))
            
            # Get inventory items that aren't in shopify
            inventory_items = list(db['inventory'].find({
                "username": current_user.username,
                "productId": {"$nin": list(shopify_products)},
                "$or": [
                    {"shProduct": None},
                    {"matched_variant": False}
                ]
            }, {
                "_id": 0,
                "name": 1,
                "productId": 1,
                "condition": 1,
                "quantity": 1,
                "lowPrice": 1,
                "foil": 1,
                "expansionName": 1
            }))
            
            logger.info(f"Found {len(inventory_items)} inventory items for user {current_user.username}")
            return jsonify({"items": inventory_items})
            
        except Exception as e:
            logger.error(f"Error fetching staged items: {str(e)}")
            return jsonify({"error": str(e)}), 500

    @update_catalog_bp.route('/api/unique-languages', methods=['POST'])
    @login_required
    def get_unique_languages():
        try:
            data = request.json
            selected_product_ids = data.get('selectedProductIds', [])

            if not selected_product_ids:
                return jsonify({"message": "No products selected", "type": "error"}), 400

            # Query catalog collection for selected products
            catalog_query = {'productId': {'$in': selected_product_ids}}
            catalog_products = list(catalog_collection.find(catalog_query))

            # Extract unique languages from SKUs
            unique_languages = set()
            for product in catalog_products:
                for sku in product.get('skus', []):
                    if isinstance(sku, dict) and 'langAbbr' in sku:
                        unique_languages.add(sku['langAbbr'])

            logger.info(f"Found unique languages: {unique_languages}")
            return jsonify({"languages": sorted(list(unique_languages))})

        except Exception as e:
            logger.error(f"Error getting unique languages: {str(e)}")
            return jsonify({"message": str(e), "type": "error"}), 500

    @update_catalog_bp.route('/api/process', methods=['POST'])
    @login_required
    def process_game():
        data = request.json
        selected_product_ids = data.get('selectedProducts') or data.get('selectedProductIds', [])
        selected_conditions = data.get('selectedConditions', [])
        selected_languages = data.get('selectedLanguages', [])
        username = current_user.username

        logger.info(f"Number of selected products: {len(selected_product_ids)}")
        logger.info(f"Selected conditions: {selected_conditions}")

        if not selected_product_ids:
            logger.warning("No products selected for processing.")
            return jsonify({"message": "No products selected. Please select at least one product.", "type": "error"}), 400

        if not selected_conditions:
            logger.warning("No conditions selected for processing.")
            return jsonify({"message": "No conditions selected. Please select at least one condition.", "type": "error"}), 400
        
        # Save the selected conditions to the user profile
        try:
            settings = {"selected_conditions": selected_conditions}
            current_user.set_shopify_update_catalog_settings(settings)
            logger.info(f"Saved selected conditions to user profile: {selected_conditions}")
        except Exception as e:
            logger.error(f"Error saving conditions to user profile: {str(e)}")
            
        # Check if user is on a monthly, annual, or lifetime package
        subscription_name = current_user.get_subscription_name()
        logger.info(f"User {username} has subscription: {subscription_name}")

        # Apply limit only if the user is on a 'Free' subscription (case-insensitive)
        # Make sure we're not limiting users on Monthly Basic, Annual Basic, Monthly, Annual, or Lifetime packages
        is_limited_subscription = subscription_name and subscription_name.lower() == 'free'
        
        # Ensure Monthly Basic users aren't limited
        if subscription_name and ('monthly' in subscription_name.lower() or 
                                 'annual' in subscription_name.lower() or 
                                 'lifetime' in subscription_name.lower()):
            is_limited_subscription = False
            
        logger.info(f"User {username} has limited subscription: {is_limited_subscription}")
        
        if is_limited_subscription:
            if len(selected_product_ids) > 10:
                logger.warning(f"User {username} with Free subscription attempted to select {len(selected_product_ids)} items (limit is 10).")
                return jsonify({
                    "message": "Your current Free subscription only allows you to select up to 10 items at a time. Please upgrade to select more items.",
                    "type": "error"
                }), 403

        # Get catalog products first
        catalog_query = {'productId': {'$in': selected_product_ids}}
        catalog_products = list(catalog_collection.find(
            catalog_query,
            {
                '_id': 0,
                'productId': 1,
                'idProduct': 1,
                'name': 1,
                'expansionName': 1,
                'gameName': 1,
                'set_name': 1,
                'rarity': 1,
                'lang': 1,
                'type_line': 1,
                'oracle_text': 1,
                'abbreviation': 1,
                'language': 1,
                'metafieldGames': 1,
                'metafieldLegalities': 1,
                'extendedData': 1,
                'description': 1,
                'number': 1,
                'isSingle': 1,
                'isSealed': 1,
                'image': 1,
                'skus': 1,
                'upc': 1,  # Add UPC field
                'barcode': 1  # Add barcode field
            }
        ))

        # Create a map of existing catalog products by productId
        catalog_map = {str(p['productId']): p for p in catalog_products}
        
        # Get inventory items
        inventory_query = {
            'productId': {'$in': selected_product_ids},
            'username': current_user.username,
            '$or': [
                {'shProduct': None},
                {'matched_variant': False}
            ]
        }
        inventory_items = list(db['inventory'].find(inventory_query, {'_id': 0}))

        # Process inventory items
        for inventory_item in inventory_items:
            product_id = str(inventory_item['productId'])
            
            # Get or create the catalog entry
            if product_id in catalog_map:
                catalog_item = catalog_map[product_id]
            else:
                # Get base catalog data for this product
                catalog_item = catalog_collection.find_one(
                    {'productId': inventory_item['productId']},
                    {'_id': 0}
                )
                if catalog_item:
                    catalog_map[product_id] = catalog_item

            if catalog_item:
                # Ensure skus list exists
                if 'skus' not in catalog_item:
                    catalog_item['skus'] = []

                # Map condition from NM to Near Mint
                condition_map = {
                    'NM': 'Near Mint',
                    'LP': 'Lightly Played',
                    'MP': 'Moderately Played',
                    'HP': 'Heavily Played',
                    'DMG': 'Damaged'
                }
                
                # Create SKU for the inventory item
                sku = {
                    'langAbbr': 'EN',
                    'condName': condition_map.get(inventory_item.get('condition', 'NM'), 'Near Mint'),
                    'printingName': inventory_item.get('foil', 'Normal'),
                    'lowPrice': inventory_item.get('lowPrice', 0.00),
                    'skuId': inventory_item.get('skuId', str(inventory_item['productId']))
                }
                
                # Add SKU if it doesn't exist
                sku_exists = any(
                    existing_sku.get('skuId') == sku['skuId'] 
                    for existing_sku in catalog_item['skus']
                )
                if not sku_exists:
                    catalog_item['skus'].append(sku)

        # Get final list of products to process
        all_products = list(catalog_map.values())

        logger.info(f"Number of catalog products found: {len(catalog_products)}")
        logger.info(f"Number of inventory items found: {len(inventory_items)}")
        logger.info(f"Total products to process: {len(all_products)}")

        if not all_products:
            logger.info("No products found for the selected product IDs.")
            return jsonify({"message": "No products found for the given selection", "type": "error"}), 400

        try:
            for product in all_products:
                logger.info(f"Processing product: {product.get('productId')} - {product.get('name')}")
            
            # Define output directory
            root_dir = os.path.dirname(os.path.abspath(__file__))
            output_dir = os.path.join(root_dir, "..", "shopify_json_files", username)
            
            # Process the products and generate JSON files
            response = process_products(all_products, username, selected_conditions, output_dir, selected_languages)
            
            # Add file count to response
            if os.path.exists(output_dir):
                json_files = [f for f in os.listdir(output_dir) if f.endswith('.json')]
                response["fileCount"] = len(json_files)
                response["message"] = f"Successfully created {len(json_files)} JSON files"
            return jsonify(response)
        except Exception as e:
            logger.error(f"Error in generate_json_files: {str(e)}")
            return jsonify({"message": f"An error occurred: {str(e)}", "type": "error"}), 500

    def get_condition_order(condition):
        order = {
            "Near Mint": 1,
            "Lightly Played": 2,
            "Moderately Played": 3,
            "Heavily Played": 4,
            "Damaged": 5
        }
        return order.get(condition, 6)  # Unknown conditions go at the end

    def construct_title(doc, title_format):
        logger.info(f"Starting title construction for product {doc.get('productId')} with format settings: {title_format}")
        
        # For sealed items, just use the name directly without any modifications
        if doc.get("isSealed"):
            raw_name = doc.get('name', 'No Title')
            logger.info(f"Using original name for sealed item: {raw_name}")
            return raw_name
        
        # For singles, apply the title formatting
        # Get raw values first
        raw_name = doc.get('name', 'No Title')
        raw_number = str(doc.get('number', ''))  # Convert to string and handle None case
        raw_abbreviation = doc.get('abbreviation')
        
        # No longer cleaning the name - use it as is
        logger.info(f"Raw values - name: {raw_name}, number: {raw_number}, abbreviation: {raw_abbreviation}")
        
        # Get expansion name
        expansion_name = doc.get('expansionName', '')
        
        # Format components
        components = {
            'name': raw_name,
            'number': f"({raw_number})" if raw_number else None,
            'abbreviation': f"({raw_abbreviation})" if raw_abbreviation else None,
            'expansion': f"({expansion_name})" if expansion_name else None
        }
        
        logger.info(f"Formatted components: {components}")
        
        # Build title based on format preferences
        title_parts = []
        
        # Check if title format has order and include settings
        if title_format and 'order' in title_format:
            # Use user-defined order and inclusion settings
            for part in title_format.get('order', ['name', 'number', 'expansion', 'abbreviation']):
                include_key = f'include{part.capitalize()}'
                if title_format.get(include_key, True) and components.get(part):
                    title_parts.append(components[part])
        else:
            # Default ordering
            # Always include name
            title_parts.append(components['name'])
            
            # Add number if it exists and includeNumber is true (default to true)
            if components['number'] and title_format.get('includeNumber', True):
                title_parts.append(components['number'])
                
            # Add expansion if it exists and includeExpansion is true (default to true)
            if components['expansion'] and title_format.get('includeExpansion', True):
                title_parts.append(components['expansion'])
                
            # Add abbreviation if it exists and includeAbbreviation is true (default to true)
            if components['abbreviation'] and title_format.get('includeAbbreviation', True):
                title_parts.append(components['abbreviation'])
            
        logger.info(f"Title parts: {title_parts}")
        
        final_title = ' '.join(title_parts)  # Join with space
        logger.info(f"Final constructed title: {final_title}")
        
        return final_title

    def save_json_file(products, output_dir, batch_number=1):
        """Save products to a JSON file with size limit of 10MB"""
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"products_batch{batch_number}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)
        
        # Convert to JSON string to check size
        json_data = json.dumps(products, ensure_ascii=False, indent=2)
        size_mb = len(json_data.encode('utf-8')) / (1024 * 1024)  # Size in MB
        
        logger.info(f"Saving JSON file: {filepath} with {len(products)} products (size: {size_mb:.2f}MB)")
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(json_data)
        logger.info(f"Successfully saved JSON file: {filepath}")
        
        return size_mb

    @update_catalog_bp.route('/api/smoshey-records', methods=['GET'])
    @login_required
    def get_smoshey_records():
        if current_user.username != "Xavier":
            return jsonify({"error": "Unauthorized"}), 403
            
        try:
            # Connect to smoshey database
            smoshey_db = mongo_client['smoshey']
            instock_collection = smoshey_db['inStock']
            
            # Get all productIds from user's shopify collection
            shopify_products = set(doc["productId"] for doc in shopify_collection.find(
                {"username": current_user.username},
                {"productId": 1, "_id": 0}
            ))
            
            # Get records from inStock collection that aren't in shopify
            missing_records = list(instock_collection.find(
                {
                    "productId": {"$nin": list(shopify_products)}
                },
                {
                    "_id": 0,
                    "productId": 1,
                    "Card Name": 1
                }
            ))
            
            return jsonify({
                'count': len(missing_records),
                'records': missing_records
            })
            
        except Exception as e:
            logger.error(f"Error fetching smoshey records: {str(e)}")
            return jsonify({"error": str(e)}), 500

    @update_catalog_bp.route('/api/inventory-priority-items', methods=['GET'])
    @login_required
    def get_inventory_priority_items():
        """Get items from user's inventory that aren't in their Shopify store"""
        try:
            # Get all productIds from user's shopify collection
            shopify_products = set(doc["productId"] for doc in shopify_collection.find(
                {"username": current_user.username},
                {"productId": 1, "_id": 0}
            ))
            
            logger.info(f"Found {len(shopify_products)} products in user's Shopify store")
            
            # Get unique productIds from user's inventory that aren't in shopify
            inventory_pipeline = [
                {
                    "$match": {
                        "username": current_user.username,
                        "productId": {"$nin": list(shopify_products)}
                    }
                },
                {
                    "$group": {
                        "_id": "$productId",
                        "productId": {"$first": "$productId"}
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "productId": 1
                    }
                }
            ]
            
            inventory_product_ids = [doc["productId"] for doc in db['inventory'].aggregate(inventory_pipeline)]
            logger.info(f"Found {len(inventory_product_ids)} unique products in inventory not in Shopify")
            
            if not inventory_product_ids:
                return jsonify({
                    'count': 0,
                    'products': []
                })
            
            # Get catalog data for these products
            catalog_products = list(catalog_collection.find(
                {"productId": {"$in": inventory_product_ids}},
                {
                    "_id": 0,
                    "productId": 1,
                    "idProduct": 1,
                    "name": 1,
                    "expansionName": 1,
                    "number": 1,
                    "rarity": 1,
                    "isSingle": 1,
                    "isSealed": 1,
                    "image": 1,
                    "releasedOn": 1
                }
            ))
            
            logger.info(f"Found {len(catalog_products)} catalog products for inventory items")
            
            return jsonify({
                'count': len(catalog_products),
                'products': catalog_products
            })
            
        except Exception as e:
            logger.error(f"Error fetching inventory priority items: {str(e)}")
            return jsonify({"error": str(e)}), 500

    def process_products(catalog_products, username, selected_conditions, output_dir, selected_languages):
        products_by_expansion = {}
        title_format = current_user.get_title_format()
        logger.info(f"User {username} title format settings: {title_format}")
        
        # Initialize pricing calculator if saautopricing is available
        pricing_calculator = None
        user_currency = "USD"
        exchange_rate = 1.0
        tcgplayer_api_key = None
        
        if SAAUTOPRICING_AVAILABLE:
            try:
                # Get user profile for pricing settings
                user_profile = db['user'].find_one({'username': username})
                if user_profile:
                    # Get user currency and exchange rate
                    user_currency = user_profile.get('currency', 'USD')
                    exchange_rate = get_exchange_rate(user_currency)
                    logger.info(f"User currency: {user_currency}, Exchange rate: {exchange_rate}")
                    
                    # Get pricing settings
                    settings = prepare_shopify_settings(user_profile)
                    pricing_calculator = PricingCalculator(settings, user_currency)
                    logger.info(f"Created pricing calculator with settings: {settings}")
                    
                    # Get TCGPlayer API key
                    tcgplayer_key_doc = db['tcgplayerKey'].find_one({})
                    if tcgplayer_key_doc:
                        tcgplayer_api_key = tcgplayer_key_doc.get('latestKey')
                        logger.info("Retrieved TCGPlayer API key")
                    else:
                        logger.warning("TCGPlayer API key not found")
            except Exception as e:
                logger.error(f"Error initializing pricing calculator: {str(e)}")
                pricing_calculator = None

        for doc in catalog_products:
            logger.info(f"Processing product {doc.get('productId')} - {doc.get('name')}")
            logger.info(f"SKUs before processing: {doc.get('skus', [])}")
            
            expansion_name = doc.get("expansionName", "Unknown Expansion")
            if expansion_name not in products_by_expansion:
                products_by_expansion[expansion_name] = []
        
            # Initialize metafield_games and metafield_legalities before the if-else block
            metafield_games = doc.get("metafieldGames", [])
            metafield_legalities = doc.get("metafieldLegalities", {})
            
            # Generate tags
            if doc.get("isSealed"):
                # For sealed items, only include game, abbreviation, and expansion name
                # No need to include other tags for sealed items
                tags = [
                    doc.get("gameName", ""),
                    doc.get("abbreviation", ""),
                    doc.get("expansionName", "")
                ]
            else:
                # For singles, keep the original tag generation but exclude oracle text
                tags = [
                    doc.get("set_name", ""),
                    doc.get("rarity", ""),
                    doc.get("lang", ""),
                    doc.get("type_line", ""),
                    # Removed oracle_text as requested
                    doc.get("gameName", ""),
                    doc.get("expansionName", ""),
                    doc.get("abbreviation", ""),
                    doc.get("language", "")
                ]
                
                # Add metafieldGames as individual tags
                tags.extend(metafield_games)

                # Add metafieldLegalities as tags
                for format, legality in metafield_legalities.items():
                    if legality.lower() == "legal":
                        tags.append(format)

            # Build body HTML with table structure
            body_html = '<table class="singles-description-table" xmlns="http://www.w3.org/1999/html"><tbody>'
            
            # Get extended data
            extended_data = doc.get("extendedData", [])
            extended_data_dict = {data.get('displayName', ''): data.get('value', '') for data in extended_data}
            
            # Add extendedData items as tags (except Description) - only for singles
            if not doc.get("isSealed"):
                for data in extended_data:
                    name = data.get('name', '')
                    value = data.get('value', '')
                    
                    display_name = data.get('displayName', '')
                    # Skip Description, card_text, and Oracle Text fields
                    if name == "Description" or name == "card_text" or name == "Oracle Text" or display_name == "Oracle Text":
                        continue
                        
                    # Add value as a tag if it exists
                    if value:
                        tags.append(value)

            # Remove empty tags and join
            tags = [tag for tag in tags if tag and tag.strip()]
            tags_string = ', '.join(tags)
            
            # Add all extended data to table
            if extended_data:
                for data in extended_data:
                    display_name = data.get('displayName', '')
                    value = data.get('value', '')
                    if display_name and value and display_name not in ['Oracle Text', 'Reverse Oracle Text', 'Reverse Type']:
                        body_html += f"""
      <tr>
          <td>{display_name}:</td>
          <td>{value}</td>
      </tr>"""
            
            # Add basic details if not already included in extended data
            basic_details = [
                ('Set', doc.get('expansionName', '')),
                ('Rarity', doc.get('rarity', '')),
                ('Number', doc.get('number', '')),
                ('Language', doc.get('language', ''))
            ]
            
            for label, value in basic_details:
                if value and label not in [data.get('displayName') for data in extended_data]:
                    body_html += f"""
      <tr>
          <td>{label}:</td>
          <td>{value}</td>
      </tr>"""
            
            body_html += """
</tbody>
</table>"""

            # Oracle text section
            oracle_text = extended_data_dict.get('Oracle Text', doc.get('description', ''))
            if oracle_text:
                body_html += f"""
<div class="single-description-div">
        <div class="oracle-text">
            {oracle_text}
        </div>
</div>"""

            # Check for reverse/back face data
            reverse_type = extended_data_dict.get('Reverse Type', '')
            reverse_oracle = extended_data_dict.get('Reverse Oracle Text', '')
            
            if reverse_type or reverse_oracle:
                # Add reverse side table
                body_html += """
<table class="singles-reverse-description-table">
<tbody>"""
                if reverse_type:
                    body_html += f"""
      <tr>
          <td>Reverse Type:</td>
          <td>{reverse_type}</td>
      </tr>"""
                body_html += """
</tbody>
</table>"""
                
                # Add reverse oracle text if available
                if reverse_oracle:
                    body_html += f"""
<div class="single-description-div">
        <div class="reverseOracle-text">
            {reverse_oracle}
        </div>
</div>"""

            # Add metadata div
            product_id = doc.get("productId", "N/A")
            game_name = doc.get('gameName', '')
            game_name_to_cardtype = {
                'Magic: The Gathering': 'mtg',
                'Pokemon': 'pokemon',
                'Yu-Gi-Oh!': 'yugioh',
                'Akora TCG': 'akora',
                'One Piece Card Game': 'onepiece'
            }
            data_cardtype = game_name_to_cardtype.get(game_name, 'other')
            
            body_html += f'''
        <div class="catalogMetaData" style="visibility: hidden;" 
             data-cardtype="{data_cardtype}" 
             data-cardid="5" 
             data-tcgid="{product_id}" 
             data-lastupdated="{datetime.now().isoformat()}">
        </div>
        '''

            # Construct title using user's format settings
            title = construct_title(doc, title_format)

            variants = []
            logger.info(f"Processing variants for product {doc.get('productId')} - {doc.get('name')}")
            logger.info(f"Selected conditions: {selected_conditions}")
            logger.info(f"Selected languages: {selected_languages}")
            
            # Ensure skus list exists
            skus = doc.get("skus", [])
            if not isinstance(skus, list):
                skus = []
                logger.warning(f"SKUs for product {doc.get('productId')} was not a list. Defaulting to empty list.")
            
            # Handle sealed items differently
            if doc.get("isSealed"):
                logger.info(f"Processing sealed item: {doc.get('productId')} - {doc.get('name')}")
                # For sealed items, create a single variant without condition/language filtering
                
                # Use the product ID as SKU ID if no SKUs exist
                sku_id = doc.get("productId")
                if skus and isinstance(skus[0], dict):
                    sku_id = skus[0].get("skuId", sku_id)
                    low_price = skus[0].get("lowPrice", 0.00)
                else:
                    low_price = 0.00
                
                # Format price
                try:
                    price = "{:.2f}".format(float(low_price))
                except (ValueError, TypeError):
                    price = "0.00"
                
                # Extract UPC/barcode from top-level fields for barcode
                upc_barcode = doc.get('upc', '') or doc.get('barcode', '')
                if upc_barcode:
                    upc_barcode = str(upc_barcode)

                # Use UPC as barcode if available, otherwise fall back to SKU ID
                barcode_value = upc_barcode if upc_barcode else str(sku_id)

                logger.info(f"Creating sealed variant with price: {price}, barcode: {barcode_value}")
                variants.append({
                    "title": "Sealed",  # Simple title for sealed items
                    "price": price,
                    "sku": str(sku_id),  # Keep SKU as skuId
                    "barcode": barcode_value,  # Use UPC if available, otherwise skuId
                    "weight": 0.3,
                    "weightUnit": "GRAMS",
                    "options": ["Sealed"],
                    "requiresShipping": True,
                    "inventoryManagement": "SHOPIFY",
                    "inventoryPolicy": "DENY",
                    "condition_order": 1  # All sealed items get same condition order
                })
            else:
                # Process singles with condition filtering
                for sku in skus:
                    logger.info(f"Processing SKU: {sku}")
                    if not isinstance(sku, dict):
                        continue
                        
                    # Map languageId to language abbreviation
                    language_map = {
                        1: "EN",
                        # Add other language mappings as needed
                    }
                    lang_abbr = language_map.get(sku.get("languageId"), sku.get("langAbbr", "EN"))
                    
                    # Skip if languages are selected and this language isn't in the selection
                    if selected_languages and selected_languages != [] and lang_abbr not in selected_languages:
                        logger.info(f"Skipping language {lang_abbr} - not in selected languages")
                        continue
                    
                    # Map conditionId to condition name
                    condition_id_map = {
                        1: "Near Mint",
                        2: "Lightly Played",
                        3: "Moderately Played",
                        4: "Heavily Played",
                        5: "Damaged"
                    }
                    condition = condition_id_map.get(sku.get('conditionId'), sku.get('condName', ''))
                    logger.info(f"Processing SKU condition: {condition}")
                    
                    # Special handling for user Dezmu - filter conditions first
                    if username == "Dezmu":
                        if condition not in ["Near Mint", "Lightly Played"]:
                            logger.info(f"Skipping condition {condition} for user Dezmu")
                            continue
                    # Special handling for user Xavier
                    elif username == "Xavier":
                        if condition != "Near Mint":
                            logger.info(f"Skipping condition {condition} for user Xavier")
                            continue
                    
                    # After user-specific filtering, check if condition is in selected conditions
                    if condition not in selected_conditions:
                        logger.info(f"Skipping condition {condition} - not in selected conditions")
                        continue
                        
                    printing = sku.get('printingName', '')
                    # Format variant title like warehouse:
                    # If printing is Normal, just show condition
                    # If printing is special (Foil etc), show "condition - printing"
                    if printing.lower() == 'normal':
                        variant_title = condition
                    else:
                        variant_title = f"{condition} - {printing}"
                    
                    # Calculate price using saautopricing if available
                    if pricing_calculator and tcgplayer_api_key:
                        try:
                            # Get product ID
                            product_id = str(doc.get('productId'))
                            
                            # Fetch pricing data from TCGPlayer API if not already fetched
                            if not hasattr(process_products, 'pricing_data'):
                                process_products.pricing_data = {}
                            
                            if product_id not in process_products.pricing_data:
                                product_pricing = fetch_pricing_data([product_id], tcgplayer_api_key)
                                process_products.pricing_data.update(product_pricing)
                            
                            # Get pricing data for this product
                            product_pricing = process_products.pricing_data.get(product_id, [])
                            
                            # Only include subtypes that have valid prices
                            valid_subtypes = [p.get('subTypeName') for p in product_pricing if p.get('subTypeName')]
                            
                            # Determine printing type
                            printing_type = determine_printing_type(variant_title, valid_subtypes)
                            
                            # Find the price data for this printing type
                            matched_price = None
                            for p in product_pricing:
                                if p.get('subTypeName', '').lower() == printing_type.lower():
                                    matched_price = p
                                    break
                            
                            if matched_price:
                                # Extract pricing info for this printing type and convert from USD to user currency
                                pricing_info = {}
                                
                                # Get market price
                                market_price = matched_price.get('marketPrice')
                                if market_price is not None:
                                    pricing_info['marketPrice'] = float(market_price) * exchange_rate
                                
                                # Get low price
                                low_price = matched_price.get('lowPrice')
                                if low_price is not None:
                                    pricing_info['lowPrice'] = float(low_price) * exchange_rate
                                
                                # Get mid price
                                mid_price = matched_price.get('midPrice')
                                if mid_price is not None:
                                    pricing_info['midPrice'] = float(mid_price) * exchange_rate
                                
                                # Get high price
                                high_price = matched_price.get('highPrice')
                                if high_price is not None:
                                    pricing_info['highPrice'] = float(high_price) * exchange_rate
                                
                                # Create sku_info for price calculation
                                sku_info = {
                                    'pricingInfo': pricing_info,
                                    'condName': condition,
                                    'printingName': printing_type,
                                    'skuId': sku.get('skuId'),
                                    'variantTitle': variant_title
                                }
                                
                                # Calculate price
                                calculated_price, is_missing, price_history = pricing_calculator.calculate_final_price(sku_info, doc)
                                
                                if not is_missing and calculated_price is not None:
                                    price = "{:.2f}".format(calculated_price)
                                    logger.info(f"Calculated price for {variant_title}: {price} (using saautopricing)")
                                else:
                                    # Fall back to low price if calculation fails
                                    low_price = sku.get("lowPrice")
                                    if low_price is None:
                                        price = "0.00"
                                    else:
                                        try:
                                            price = "{:.2f}".format(float(low_price))
                                        except (ValueError, TypeError):
                                            price = "0.00"
                                    logger.info(f"Using fallback price for {variant_title}: {price} (calculation failed)")
                            else:
                                # Fall back to low price if no matching price data
                                low_price = sku.get("lowPrice")
                                if low_price is None:
                                    price = "0.00" # Default before checking prices collection
                                else:
                                    try:
                                        price = "{:.2f}".format(float(low_price))
                                    except (ValueError, TypeError):
                                        price = "0.00" # Default before checking prices collection
                                logger.info(f"Using fallback price for {variant_title}: {price} (no matching price data)")
                        except Exception as e:
                            # Fall back to low price if an error occurs
                            logger.error(f"Error calculating price for {variant_title}: {str(e)}")
                            low_price = sku.get("lowPrice")
                            if low_price is None:
                                price = "0.00" # Default before checking prices collection
                            else:
                                try:
                                    price = "{:.2f}".format(float(low_price))
                                except (ValueError, TypeError):
                                    price = "0.00" # Default before checking prices collection
                            logger.info(f"Using fallback price for {variant_title}: {price} (error occurred)")
                    else:
                        # Use low price if saautopricing is not available
                        low_price = sku.get("lowPrice")
                        if low_price is None:
                            logger.warning(f"Low price is None in SKU for product {doc.get('productId')} - {doc.get('name')}. Will check prices collection.")
                            price = "0.00" # Default before checking prices collection
                        else:
                            try:
                                price = "{:.2f}".format(float(low_price))
                            except (ValueError, TypeError) as e:
                                logger.error(f"Error processing SKU price for product {doc.get('productId')} - {doc.get('name')}: {str(e)}. Will check prices collection.")
                                price = "0.00" # Default before checking prices collection

                    # Fallback: If price is still "0.00", try fetching from the 'prices' collection
                    if price == "0.00":
                        try:
                            product_id_int = int(doc.get('productId'))
                            prices_collection = db['prices']
                            price_doc = prices_collection.find_one({'productId': product_id_int})
                            
                            if price_doc and 'low' in price_doc and price_doc['low'] is not None:
                                fallback_price_val = price_doc['low']
                                try:
                                    price = "{:.2f}".format(float(fallback_price_val))
                                    logger.info(f"Using fallback price from 'prices' collection for {variant_title}: {price}")
                                except (ValueError, TypeError):
                                    logger.warning(f"Invalid price format in 'prices' collection for productId {product_id_int}. Keeping price as 0.00.")
                                    price = "0.00"
                            else:
                                logger.warning(f"No valid price found in 'prices' collection for productId {product_id_int}. Keeping price as 0.00.")
                                price = "0.00"
                                
                        except ValueError:
                             logger.error(f"Could not convert productId {doc.get('productId')} to int for prices collection lookup.")
                             price = "0.00"
                        except Exception as e_prices:
                            logger.error(f"Error querying 'prices' collection for productId {doc.get('productId')}: {str(e_prices)}")
                            price = "0.00"

                    # Extract UPC/barcode from top-level fields for barcode
                    upc_barcode = doc.get('upc', '') or doc.get('barcode', '')
                    if upc_barcode:
                        upc_barcode = str(upc_barcode)

                    # Use UPC as barcode if available, otherwise fall back to SKU ID
                    barcode_value = upc_barcode if upc_barcode else str(sku.get("skuId", ""))

                    logger.info(f"Creating variant with title: {variant_title}, final price: {price}, barcode: {barcode_value}")
                    variants.append({
                        "title": variant_title,
                        "price": price,
                        "sku": str(sku.get("skuId", "")),  # Keep SKU as skuId
                        "barcode": barcode_value,  # Use UPC if available, otherwise skuId
                        "weight": 0.3,
                        "weightUnit": "GRAMS",
                        "options": [variant_title],
                        "requiresShipping": True,
                        "inventoryManagement": "SHOPIFY",
                        "inventoryPolicy": "DENY",
                        "condition_order": get_condition_order(condition)
                    })

            # Skip products with no variants after filtering
            if not variants:
                logger.info(f"Skipping product {doc.get('productId')} - {doc.get('name')} - no valid variants after filtering")
                continue

            logger.info(f"Final variants count for product {doc.get('productId')}: {len(variants)}")

            # Sort variants by printing (Normal first, then Foil) and then by condition order
            variants.sort(key=lambda x: (
                0 if x['title'].startswith("Normal") else 1,
                x['condition_order']
            ))

            # Remove the temporary 'condition_order' key
            for variant in variants:
                del variant['condition_order']

            product_status = "DRAFT" if any(variant["price"] == "0.00" for variant in variants) else "ACTIVE"

            if doc.get('gameName') == "Magic: The Gathering":
                product_type = "MTG Single" if doc.get('isSingle') else "MTG Sealed"
            else:
                product_type = f"{doc.get('gameName', 'Unknown')} {'Single' if doc.get('isSingle') else 'Sealed'}"

            # Create metafields list starting with game
            # Ensure gameName is included in metafield_games
            game_name = doc.get("gameName", "")
            if game_name and game_name not in metafield_games:
                metafield_games.append(game_name)
                
            metafields = []  # Removed all metafields as requested

            product_data = {
                "input": {
                    "title": title,
                    "published": True,
                    "status": "ACTIVE",
                    "publishedAt": datetime.now().strftime("%Y-%m-%d"),
                    "tags": tags_string,
                    "bodyHtml": body_html,
                    "vendor": doc.get("gameName", "Unknown Vendor"),
                    "productType": product_type,
                    "variants": variants,
                    "options": ["Title"],
                    "metafields": metafields
                },
                "media": [
                    {
                        "originalSource": doc.get("image", "No Image"),
                        "alt": f"Image for {title} - {doc.get('gameName', 'Unknown Game')}",
                        "mediaContentType": "IMAGE"
                    }
                ]
            }
            
            logger.info(f"Adding product data to expansion {expansion_name}")
            products_by_expansion[expansion_name].append(product_data)

        # Combine all products into a single list
        all_products = []
        for expansion_products in products_by_expansion.values():
            all_products.extend(expansion_products)
            
        logger.info(f"Total products to save: {len(all_products)}")
        
        # Split into batches if needed to stay under 10MB limit
        MAX_SIZE_MB = 10
        current_batch = []
        batch_number = 1
        file_count = 0
        
        for product in all_products:
            current_batch.append(product)
            
            # Check if batch would exceed size limit
            test_json = json.dumps(current_batch, ensure_ascii=False, indent=2)
            current_size_mb = len(test_json.encode('utf-8')) / (1024 * 1024)
            
            # If adding this product would exceed the limit, save the batch (without this product)
            if current_size_mb > MAX_SIZE_MB:
                # Remove the last product that pushed it over the limit
                last_product = current_batch.pop()
                
                # Save current batch
                if current_batch:  # Only save if there's something to save
                    save_json_file(current_batch, output_dir, batch_number)
                    file_count += 1
                    batch_number += 1
                    
                # Start a new batch with the product that was removed
                current_batch = [last_product]
        
        # Save any remaining products
        if current_batch:
            save_json_file(current_batch, output_dir, batch_number)
            file_count += 1
            
        logger.info(f"All products processed and saved to {file_count} JSON files.")
        return {"message": f"{file_count} JSON files generated successfully. Check the directory: {output_dir}", "type": "success"}

    @update_catalog_bp.route('/api/video-games/push-to-shopify', methods=['POST'])
    @login_required
    def push_video_games_to_shopify():
        """Push selected video games to Shopify with converted prices"""
        try:
            data = request.get_json()
            game_ids = data.get('gameIds', [])
            quantity = data.get('quantity', 0)  # Default to 0 quantity

            if not game_ids:
                return jsonify({'error': 'No video games selected'}), 400

            # Validate quantity
            try:
                quantity = int(quantity)
                if quantity < 0:
                    return jsonify({'error': 'Quantity must be non-negative'}), 400
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid quantity format'}), 400

            # Get user's Shopify credentials (same logic as board games)
            user_collection = db['user']
            user_data = user_collection.find_one({'username': current_user.username})

            if not user_data:
                return jsonify({'error': 'User not found'}), 404

            shopify_access_token = user_data.get('shopifyAccessToken')
            shopify_store_name = user_data.get('shopifyStoreName')

            if not shopify_access_token or not shopify_store_name:
                return jsonify({'error': 'Shopify credentials not configured. Please set up your Shopify integration first.'}), 400

            # Get user's currency for price conversion
            user_currency = get_user_currency(current_user.username)
            logger.info(f"Pushing video games to Shopify with {user_currency} currency for user {current_user.username}")

            # Get video games from database
            videogames_collection = db['videogames']

            # Convert string IDs to ObjectIds if needed
            from bson import ObjectId
            object_ids = []
            for game_id in game_ids:
                try:
                    if isinstance(game_id, str) and len(game_id) == 24:
                        object_ids.append(ObjectId(game_id))
                    else:
                        object_ids.append(game_id)
                except:
                    object_ids.append(game_id)

            games = list(videogames_collection.find({'_id': {'$in': object_ids}}))

            if not games:
                return jsonify({'error': 'No video games found'}), 404

            # Convert prices to user's currency for all games
            for game in games:
                convert_video_game_prices_to_currency(game, user_currency)

            # Push to Shopify
            result = push_video_games_to_shopify_store(
                games, shopify_store_name, shopify_access_token, current_user.username, user_currency, quantity
            )

            return jsonify(result)

        except Exception as e:
            logger.error(f"Error pushing video games to Shopify: {str(e)}")
            return jsonify({'error': str(e)}), 500

    return update_catalog_bp


def push_video_games_to_shopify_store(video_games, store_name, access_token, username, currency, quantity=0):
    """
    Push video games directly to Shopify store via REST API with converted prices

    Args:
        video_games: List of video game documents from MongoDB
        store_name: Shopify store name
        access_token: Shopify access token
        username: Username for tracking
        currency: User's currency for pricing
        quantity: Quantity for each variant

    Returns:
        dict: Response with push results
    """
    try:
        # Shopify API setup
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': access_token
        }

        api_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/products.json"

        successful_pushes = []
        failed_pushes = []

        for game in video_games:
            try:
                # Convert video game to Shopify product format with converted prices
                product_data = convert_video_game_to_shopify_rest_format(game, currency, quantity)

                # Make API request to create product
                response = requests.post(api_url, headers=headers, json=product_data)

                if response.status_code == 201:
                    product_response = response.json()
                    successful_pushes.append({
                        'name': game.get('product-name', 'Unknown Game'),
                        'shopify_id': product_response['product']['id'],
                        'currency': currency
                    })
                    logger.info(f"Successfully pushed video game '{game.get('product-name')}' to Shopify with {currency} pricing")
                else:
                    error_msg = f"Shopify API error: {response.status_code} - {response.text}"
                    failed_pushes.append({
                        'name': game.get('product-name', 'Unknown Game'),
                        'error': error_msg
                    })
                    logger.error(f"Failed to push video game '{game.get('product-name')}': {error_msg}")

            except Exception as e:
                error_msg = f"Error processing game: {str(e)}"
                failed_pushes.append({
                    'name': game.get('product-name', 'Unknown Game'),
                    'error': error_msg
                })
                logger.error(f"Error pushing video game '{game.get('product-name')}': {error_msg}")

        # Return results
        total_games = len(video_games)
        success_count = len(successful_pushes)
        fail_count = len(failed_pushes)

        return {
            'type': 'success' if success_count > 0 else 'error',
            'message': f"Pushed {success_count}/{total_games} video games to Shopify with {currency} pricing",
            'successful_pushes': successful_pushes,
            'failed_pushes': failed_pushes,
            'currency': currency
        }

    except Exception as e:
        logger.error(f"Error in push_video_games_to_shopify_store: {str(e)}")
        return {
            'type': 'error',
            'message': f"Failed to push video games: {str(e)}"
        }


def convert_video_game_to_shopify_rest_format(game, currency, quantity=0):
    """
    Convert video game data to Shopify REST API product format with converted prices

    Args:
        game: Video game document from MongoDB
        currency: User's currency for pricing
        quantity: Quantity for each variant

    Returns:
        dict: Shopify product data
    """
    # Extract basic game information with proper string conversion
    title = str(game.get('product-name', 'Unknown Game'))
    platform = str(game.get('console-name', 'Unknown Platform'))
    genre = str(game.get('genre', 'Video Game'))
    developer = str(game.get('developer', '')) if game.get('developer') else ''
    publisher = str(game.get('publisher', '')) if game.get('publisher') else ''
    release_date = str(game.get('release-date', '')) if game.get('release-date') else ''
    image_url = str(game.get('image-url', '')) if game.get('image-url') else ''

    # Handle UPC which might be stored as an object in MongoDB
    upc_raw = game.get('upc', '')
    if isinstance(upc_raw, dict) and '$numberLong' in upc_raw:
        upc = str(upc_raw['$numberLong'])
    elif upc_raw:
        upc = str(upc_raw)
    else:
        upc = ''

    # Build product title
    product_title = f"{title} ({platform})"

    # Build description
    description_parts = [f"Platform: {platform}"]
    if genre:
        description_parts.append(f"Genre: {genre}")
    if developer:
        description_parts.append(f"Developer: {developer}")
    if publisher:
        description_parts.append(f"Publisher: {publisher}")
    if release_date:
        description_parts.append(f"Release Date: {release_date}")

    description = "<br>".join(description_parts)

    # Create variants for different conditions (prices already converted)
    variants = []
    variant_conditions = [
        ('new-price', 'New/Sealed'),
        ('cib-price', 'Complete in Box'),
        ('loose-price', 'Loose/Cart Only'),
        ('box-only-price', 'Box Only'),
        ('manual-only-price', 'Manual Only')
    ]

    for price_field, condition_name in variant_conditions:
        price_str = game.get(price_field)
        if price_str and price_str != '$0.00' and price_str != 'N/A':
            # Extract numeric price (already converted to user's currency)
            try:
                import re
                price_match = re.search(r'[\d,]+\.?\d*', price_str.replace('$', '').replace('£', '').replace('€', '').replace('C$', '').replace('A$', '').replace('NZ$', '').replace('MX$', '').replace(',', ''))
                if price_match:
                    price_value = float(price_match.group())

                    variant = {
                        'option1': condition_name,  # Use option1 for condition
                        'price': str(price_value),
                        'inventory_management': 'shopify',
                        'inventory_policy': 'deny',
                        'inventory_quantity': quantity,
                        'requires_shipping': True,
                        'taxable': True
                    }

                    if upc:
                        variant['barcode'] = str(upc)

                    variants.append(variant)
            except:
                continue

    # If no variants created, create a default one
    if not variants:
        variants.append({
            'option1': 'Default',
            'price': '0.00',
            'inventory_management': 'shopify',
            'inventory_policy': 'deny',
            'inventory_quantity': quantity,
            'requires_shipping': True,
            'taxable': True
        })

    # Build comprehensive tags
    tags = [
        'Video Game',
        platform,
        genre,
        currency,
        'Gaming',
        'Console Game'
    ]

    # Add developer and publisher as tags if available
    if developer:
        tags.append(developer)
    if publisher and publisher != developer:
        tags.append(publisher)

    # Remove duplicates and empty tags
    tags = list(set([tag for tag in tags if tag and tag.strip()]))

    # Build product data with metafields
    product_data = {
        'product': {
            'title': product_title,
            'body_html': description,
            'vendor': publisher or developer or 'Unknown',
            'product_type': 'Video Game',
            'tags': ', '.join(tags),
            'options': [
                {
                    'name': 'Condition',
                    'values': [variant['option1'] for variant in variants]
                }
            ],
            'variants': variants,
            'status': 'active',  # Create as active product
            'metafields': [
                {
                    'namespace': 'video_game',
                    'key': 'platform',
                    'value': str(platform),
                    'type': 'single_line_text_field'
                },
                {
                    'namespace': 'video_game',
                    'key': 'genre',
                    'value': str(genre),
                    'type': 'single_line_text_field'
                },
                {
                    'namespace': 'video_game',
                    'key': 'currency',
                    'value': str(currency),
                    'type': 'single_line_text_field'
                }
            ]
        }
    }

    # Add optional metafields
    if developer:
        product_data['product']['metafields'].append({
            'namespace': 'video_game',
            'key': 'developer',
            'value': str(developer),
            'type': 'single_line_text_field'
        })

    if publisher:
        product_data['product']['metafields'].append({
            'namespace': 'video_game',
            'key': 'publisher',
            'value': str(publisher),
            'type': 'single_line_text_field'
        })

    if release_date:
        product_data['product']['metafields'].append({
            'namespace': 'video_game',
            'key': 'release_date',
            'value': str(release_date),
            'type': 'single_line_text_field'
        })

    if upc:
        product_data['product']['metafields'].append({
            'namespace': 'video_game',
            'key': 'upc',
            'value': str(upc),
            'type': 'single_line_text_field'
        })

    # Add image if available
    if image_url:
        product_data['product']['images'] = [{
            'src': image_url,
            'alt': title
        }]

    return product_data


def push_board_games_to_shopify_store(board_games, store_name, access_token, username, price, quantity):
    """
    Push board games directly to Shopify store via REST API

    Args:
        board_games: List of board game documents from MongoDB
        store_name: Shopify store name
        access_token: Shopify access token
        username: Username for tracking
        price: Price for each board game
        quantity: Quantity for each board game

    Returns:
        dict: Response with push results
    """
    try:
        # Get database connection
        from pymongo import MongoClient
        import os
        mongo_client = MongoClient(os.getenv('MONGODB_URI', 'mongodb://localhost:27017/'))
        db = mongo_client['test']
        # Shopify API setup
        headers = {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': access_token
        }

        api_url = f"https://{store_name}.myshopify.com/admin/api/2023-04/products.json"

        successful_pushes = []
        failed_pushes = []

        for game in board_games:
            try:
                # Convert board game to Shopify product format
                product_data = convert_board_game_to_shopify_rest_format(game, price, quantity)

                # Make API request to create product
                response = requests.post(api_url, headers=headers, json=product_data)

                if response.status_code == 201:
                    shopify_product = response.json()
                    product_id = shopify_product['product']['id']
                    game_name = game.get('name', 'Unknown Game')

                    successful_pushes.append({
                        'game_name': game_name,
                        'shopify_id': product_id,
                        'bgg_id': game.get('id')
                    })

                    logger.info(f"Successfully created board game '{game_name}' in Shopify with ID: {product_id}")

                    # Save to shProducts collection for tracking
                    try:
                        sh_products_collection = db['shProducts']
                        sh_products_collection.insert_one({
                            'username': username,
                            'productId': product_id,
                            'productName': game_name,
                            'productType': 'Board Game',
                            'bggId': game.get('id'),
                            'createdAt': datetime.now(),
                            'source': 'board_game_import'
                        })
                    except Exception as save_error:
                        logger.warning(f"Failed to save product tracking data: {str(save_error)}")

                else:
                    error_data = response.json() if response.content else "No response content"
                    game_name = game.get('name', 'Unknown Game')
                    failed_pushes.append({
                        'game_name': game_name,
                        'error': f"HTTP {response.status_code}: {error_data}",
                        'bgg_id': game.get('id')
                    })
                    logger.error(f"Failed to create board game '{game_name}' in Shopify: {response.status_code} - {error_data}")

            except Exception as game_error:
                game_name = game.get('name', 'Unknown Game')
                failed_pushes.append({
                    'game_name': game_name,
                    'error': str(game_error),
                    'bgg_id': game.get('id')
                })
                logger.error(f"Error processing board game '{game_name}': {str(game_error)}")

        # Prepare response
        total_games = len(board_games)
        successful_count = len(successful_pushes)
        failed_count = len(failed_pushes)

        if successful_count > 0 and failed_count == 0:
            return {
                "message": f"Successfully pushed {successful_count} board games to Shopify!",
                "type": "success",
                "successful_count": successful_count,
                "failed_count": failed_count,
                "successful_pushes": successful_pushes
            }
        elif successful_count > 0 and failed_count > 0:
            return {
                "message": f"Pushed {successful_count} board games successfully, {failed_count} failed.",
                "type": "partial_success",
                "successful_count": successful_count,
                "failed_count": failed_count,
                "successful_pushes": successful_pushes,
                "failed_pushes": failed_pushes
            }
        else:
            return {
                "message": f"Failed to push all {total_games} board games to Shopify.",
                "type": "error",
                "successful_count": successful_count,
                "failed_count": failed_count,
                "failed_pushes": failed_pushes
            }

    except Exception as e:
        logger.error(f"Error pushing board games to Shopify: {str(e)}")
        return {
            "message": f"Error pushing board games to Shopify: {str(e)}",
            "type": "error"
        }


def convert_board_game_to_shopify_rest_format(game, price, quantity):
    """
    Convert a board game document to Shopify REST API format

    Args:
        game: Board game document from MongoDB
        price: Price for the board game
        quantity: Quantity for the board game

    Returns:
        dict: Shopify REST API product format
    """
    # Extract basic information
    game_name = game.get('name', 'Unknown Game')
    year_published = game.get('year_published', game.get('yearpublished', ''))
    description = game.get('description', '')

    # Clean up description (remove HTML entities)
    if description:
        description = description.replace('&#10;', '\n').replace('&pound;', '£').replace('&amp;', '&')
        # Limit description length for Shopify
        if len(description) > 5000:
            description = description[:4997] + "..."

    # Create title
    title = f"{game_name}"
    if year_published:
        title += f" ({year_published})"

    # Generate tags
    tags = []

    # Add basic tags
    tags.extend(['Board Game', 'Tabletop Game'])

    # Add year if available
    if year_published:
        tags.append(str(year_published))

    # Add categories
    categories = game.get('categories', [])
    for category in categories[:3]:  # Limit to first 3 categories
        if isinstance(category, dict) and 'value' in category:
            tags.append(category['value'])

    # Add mechanics
    mechanics = game.get('mechanics', [])
    for mechanic in mechanics[:3]:  # Limit to first 3 mechanics
        if isinstance(mechanic, dict) and 'value' in mechanic:
            tags.append(mechanic['value'])

    # Add publishers
    publishers = game.get('publishers', [])
    publisher_name = 'Unknown Publisher'
    for publisher in publishers[:2]:  # Limit to first 2 publishers
        if isinstance(publisher, dict) and 'value' in publisher:
            if publisher_name == 'Unknown Publisher':
                publisher_name = publisher['value']
            tags.append(publisher['value'])

    # Add player count tags
    min_players = game.get('min_players')
    max_players = game.get('max_players')
    if min_players and max_players:
        if min_players == max_players:
            tags.append(f"{min_players} Players")
        else:
            tags.append(f"{min_players}-{max_players} Players")

    # Add playtime tags
    min_playtime = game.get('min_playtime')
    max_playtime = game.get('max_playtime', game.get('playing_time'))
    if min_playtime and max_playtime:
        if min_playtime == max_playtime:
            tags.append(f"{min_playtime} Minutes")
        else:
            tags.append(f"{min_playtime}-{max_playtime} Minutes")

    # Add age rating
    min_age = game.get('min_age')
    if min_age:
        tags.append(f"Age {min_age}+")

    # Clean and join tags
    tags = [tag.strip() for tag in tags if tag and tag.strip()]
    tags_string = ', '.join(tags[:20])  # Limit to 20 tags for Shopify

    # Create body HTML with game details
    body_html = f"<h2>{game_name}</h2>"

    if description:
        body_html += f"<p>{description}</p>"

    # Add game details
    body_html += "<h3>Game Details</h3><ul>"

    if year_published:
        body_html += f"<li><strong>Year Published:</strong> {year_published}</li>"

    if min_players and max_players:
        if min_players == max_players:
            body_html += f"<li><strong>Players:</strong> {min_players}</li>"
        else:
            body_html += f"<li><strong>Players:</strong> {min_players}-{max_players}</li>"

    if min_playtime and max_playtime:
        if min_playtime == max_playtime:
            body_html += f"<li><strong>Playing Time:</strong> {min_playtime} minutes</li>"
        else:
            body_html += f"<li><strong>Playing Time:</strong> {min_playtime}-{max_playtime} minutes</li>"

    if min_age:
        body_html += f"<li><strong>Minimum Age:</strong> {min_age}+</li>"

    # Add BGG rating and rank
    average_rating = game.get('average', game.get('bayesaverage'))
    if average_rating:
        body_html += f"<li><strong>BGG Rating:</strong> {round(float(average_rating), 1)}/10</li>"

    rank = game.get('rank')
    if rank and rank != 'Not Ranked':
        body_html += f"<li><strong>BGG Rank:</strong> #{rank}</li>"

    body_html += "</ul>"

    # Add designers and publishers
    designers = game.get('designers', [])
    if designers:
        designer_names = [d.get('value', '') for d in designers if isinstance(d, dict)]
        if designer_names:
            body_html += f"<p><strong>Designers:</strong> {', '.join(designer_names[:5])}</p>"

    if publishers:
        publisher_names = [p.get('value', '') for p in publishers if isinstance(p, dict)]
        if publisher_names:
            body_html += f"<p><strong>Publishers:</strong> {', '.join(publisher_names[:5])}</p>"

    # Create variants with user-specified price and quantity
    variants = [{
        "option1": "Standard Edition",
        "price": f"{price:.2f}",  # Use user-specified price
        "sku": f"BG-{game.get('id', game.get('_id', 'UNKNOWN'))}",
        "inventory_quantity": quantity,  # Use user-specified quantity
        "requires_shipping": True,
        "inventory_management": "shopify",  # Always track inventory on Shopify
        "inventory_policy": "deny",  # Deny orders when out of stock
        "weight": 1000,  # Default 1kg
        "weight_unit": "g"
    }]

    # Get the best image URL
    image_url = game.get('image_url', game.get('thumbnail_url', ''))
    images = []
    if image_url:
        images.append({"src": image_url})

    # Create the REST API product data structure
    product_data = {
        "product": {
            "title": title,
            "body_html": body_html,
            "vendor": publisher_name,
            "product_type": "Board Game",
            "tags": tags_string,
            "variants": variants,
            "options": [{"name": "Edition", "values": ["Standard Edition"]}],
            "images": images,
            "published": True,
            "status": "active"
        }
    }

    return product_data


def convert_board_game_to_shopify_product(game):
    """
    Convert a board game document to Shopify product format

    Args:
        game: Board game document from MongoDB

    Returns:
        dict: Shopify product format
    """
    # Extract basic information
    game_name = game.get('name', 'Unknown Game')
    year_published = game.get('year_published', game.get('yearpublished', ''))
    description = game.get('description', '')

    # Clean up description (remove HTML entities)
    if description:
        description = description.replace('&#10;', '\n').replace('&pound;', '£').replace('&amp;', '&')

    # Create title
    title = f"{game_name}"
    if year_published:
        title += f" ({year_published})"

    # Generate tags
    tags = []

    # Add basic tags
    tags.extend(['Board Game', 'Tabletop Game'])

    # Add year if available
    if year_published:
        tags.append(str(year_published))

    # Add categories
    categories = game.get('categories', [])
    for category in categories[:5]:  # Limit to first 5 categories
        if isinstance(category, dict) and 'value' in category:
            tags.append(category['value'])

    # Add mechanics
    mechanics = game.get('mechanics', [])
    for mechanic in mechanics[:5]:  # Limit to first 5 mechanics
        if isinstance(mechanic, dict) and 'value' in mechanic:
            tags.append(mechanic['value'])

    # Add publishers
    publishers = game.get('publishers', [])
    for publisher in publishers[:3]:  # Limit to first 3 publishers
        if isinstance(publisher, dict) and 'value' in publisher:
            tags.append(publisher['value'])

    # Add designers
    designers = game.get('designers', [])
    for designer in designers[:3]:  # Limit to first 3 designers
        if isinstance(designer, dict) and 'value' in designer:
            tags.append(designer['value'])

    # Add player count tags
    min_players = game.get('min_players')
    max_players = game.get('max_players')
    if min_players and max_players:
        if min_players == max_players:
            tags.append(f"{min_players} Players")
        else:
            tags.append(f"{min_players}-{max_players} Players")

    # Add playtime tags
    min_playtime = game.get('min_playtime')
    max_playtime = game.get('max_playtime', game.get('playing_time'))
    if min_playtime and max_playtime:
        if min_playtime == max_playtime:
            tags.append(f"{min_playtime} Minutes")
        else:
            tags.append(f"{min_playtime}-{max_playtime} Minutes")

    # Add age rating
    min_age = game.get('min_age')
    if min_age:
        tags.append(f"Age {min_age}+")

    # Add BGG rank if available
    rank = game.get('rank')
    if rank and rank != 'Not Ranked':
        tags.append(f"BGG Rank {rank}")

    # Add rating if available
    average_rating = game.get('average', game.get('bayesaverage'))
    if average_rating:
        rating_rounded = round(float(average_rating), 1)
        tags.append(f"Rating {rating_rounded}")

    # Clean and join tags
    tags = [tag.strip() for tag in tags if tag and tag.strip()]
    tags_string = ', '.join(tags)

    # Create body HTML with game details
    body_html = f"<h2>{game_name}</h2>"

    if description:
        body_html += f"<p>{description}</p>"

    # Add game details
    body_html += "<h3>Game Details</h3><ul>"

    if year_published:
        body_html += f"<li><strong>Year Published:</strong> {year_published}</li>"

    if min_players and max_players:
        if min_players == max_players:
            body_html += f"<li><strong>Players:</strong> {min_players}</li>"
        else:
            body_html += f"<li><strong>Players:</strong> {min_players}-{max_players}</li>"

    if min_playtime and max_playtime:
        if min_playtime == max_playtime:
            body_html += f"<li><strong>Playing Time:</strong> {min_playtime} minutes</li>"
        else:
            body_html += f"<li><strong>Playing Time:</strong> {min_playtime}-{max_playtime} minutes</li>"

    if min_age:
        body_html += f"<li><strong>Minimum Age:</strong> {min_age}+</li>"

    if average_rating:
        body_html += f"<li><strong>BGG Rating:</strong> {round(float(average_rating), 1)}/10</li>"

    if rank and rank != 'Not Ranked':
        body_html += f"<li><strong>BGG Rank:</strong> #{rank}</li>"

    body_html += "</ul>"

    # Add designers and publishers
    if designers:
        designer_names = [d.get('value', '') for d in designers if isinstance(d, dict)]
        if designer_names:
            body_html += f"<p><strong>Designers:</strong> {', '.join(designer_names)}</p>"

    if publishers:
        publisher_names = [p.get('value', '') for p in publishers if isinstance(p, dict)]
        if publisher_names:
            body_html += f"<p><strong>Publishers:</strong> {', '.join(publisher_names)}</p>"

    # Create variants (single variant for now)
    variants = [{
        "option1": "Standard Edition",
        "price": "0.00",  # Price to be set manually
        "sku": f"BG-{game.get('id', game.get('_id', 'UNKNOWN'))}",
        "inventory_quantity": 0,
        "requires_shipping": True,
        "inventory_management": "shopify",
        "weight": 0,
        "weight_unit": "kg"
    }]

    # Create metafields for board game specific data
    metafields = [
        {
            "namespace": "BoardGameSync",
            "key": "BGG ID",
            "value": str(game.get('id', '')),
            "type": "single_line_text_field"
        },
        {
            "namespace": "BoardGameSync",
            "key": "Year Published",
            "value": str(year_published) if year_published else '',
            "type": "single_line_text_field"
        },
        {
            "namespace": "BoardGameSync",
            "key": "Min Players",
            "value": str(min_players) if min_players else '',
            "type": "number_integer"
        },
        {
            "namespace": "BoardGameSync",
            "key": "Max Players",
            "value": str(max_players) if max_players else '',
            "type": "number_integer"
        },
        {
            "namespace": "BoardGameSync",
            "key": "Playing Time",
            "value": str(max_playtime) if max_playtime else '',
            "type": "number_integer"
        },
        {
            "namespace": "BoardGameSync",
            "key": "Min Age",
            "value": str(min_age) if min_age else '',
            "type": "number_integer"
        },
        {
            "namespace": "BoardGameSync",
            "key": "BGG Rating",
            "value": str(round(float(average_rating), 2)) if average_rating else '',
            "type": "number_decimal"
        },
        {
            "namespace": "BoardGameSync",
            "key": "BGG Rank",
            "value": str(rank) if rank and rank != 'Not Ranked' else '',
            "type": "single_line_text_field"
        }
    ]

    # Get the best image URL
    image_url = game.get('image_url', game.get('thumbnail_url', ''))

    # Create the product data structure
    product_data = {
        "input": {
            "title": title,
            "published": True,
            "status": "ACTIVE",
            "publishedAt": datetime.now().strftime("%Y-%m-%d"),
            "tags": tags_string,
            "bodyHtml": body_html,
            "vendor": publishers[0].get('value', 'Unknown Publisher') if publishers else 'Unknown Publisher',
            "productType": "Board Game",
            "variants": variants,
            "options": ["Edition"],
            "metafields": metafields
        },
        "media": [
            {
                "originalSource": image_url,
                "alt": f"Image for {game_name}",
                "mediaContentType": "IMAGE"
            }
        ] if image_url else []
    }

    return product_data
