{% extends "base.html" %}

{% block title %}Shopify Update Catalog{% endblock %}

{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
        font-family: 'Roboto', sans-serif;
    }

    .main-content {
        background-color: #6b21a8;
    }

    /* Typography */
    h5, h6, .h5, .h6 {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        color: #ffffff;
    }

    /* Card styling */
    .card {
        background-color: #1e293b;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
        border: none;
    }

    .card-header {
        padding: 15px 20px;
        font-size: 18px;
        font-weight: 600;
        color: #ffffff;
        background-color: #1e293b;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .card-body {
        padding: 20px;
    }

    /* Button styling */
    .btn {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background-color: #e91e63;
        border-color: #e91e63;
    }

    .btn-primary:hover {
        background-color: #c2185b;
        border-color: #c2185b;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #5a6268;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-outline-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    /* Table styling */
    .table {
        color: white;
    }

    .table thead th {
        background-color: #1a202c;
        color: white;
        border-bottom: 2px solid #4a5568;
        padding: 15px;
        font-weight: 600;
        font-family: 'Poppins', sans-serif;
    }

    .table tbody tr:hover {
        background-color: #3a4a5c;
    }

    .table td, .table th {
        border-color: #4a5568;
        padding: 15px;
        vertical-align: middle;
    }

    /* Badge styling */
    .badge {
        padding: 6px 12px;
        font-weight: 500;
        border-radius: 20px;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    .bg-success {
        background-color: #28a745 !important;
    }

    .bg-warning {
        background-color: #ffc107 !important;
        color: #212529 !important;
    }

    .bg-danger {
        background-color: #dc3545 !important;
    }

    .bg-info {
        background-color: #17a2b8 !important;
    }

    .bg-primary {
        background-color: #007bff !important;
    }

    .bg-secondary {
        background-color: #6c757d !important;
    }

    /* Form controls */
    .form-control, .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #ffffff;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        background-color: #2d3748;
        border-color: #e91e63;
        color: #ffffff;
        box-shadow: 0 0 0 0.25rem rgba(233, 30, 99, 0.25);
    }

    /* Accordion styling */
    .accordion-item {
        background-color: #1e293b;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 10px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .accordion-button {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        padding: 15px 20px;
        background-color: #1e293b;
        color: #ffffff;
    }

    .accordion-button:not(.collapsed) {
        background-color: #2d3748;
        color: #ffffff;
    }

    .accordion-button:focus {
        box-shadow: none;
        border-color: rgba(255, 255, 255, 0.1);
    }

    /* Nav tabs styling */
    .nav-tabs {
        border-bottom: 1px solid #4a5568;
    }

    .nav-tabs .nav-link {
        border-radius: 8px 8px 0 0;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        padding: 10px 20px;
        transition: all 0.3s ease;
        border: 1px solid transparent;
        margin-right: 5px;
        color: #94a3b8;
    }

    .nav-tabs .nav-link.active {
        background-color: #1e293b;
        border-color: #4a5568 #4a5568 transparent;
        color: #ffffff;
        font-weight: 600;
        box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    }

    .nav-tabs .nav-link:hover:not(.active):not([disabled]) {
        border-color: rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.05);
        color: #ffffff;
        transform: translateY(-2px);
    }

    /* Under construction badge styling */
    .nav-link .badge.bg-danger {
        font-size: 0.7rem;
        margin-top: 10px;
        z-index: 10;
        white-space: nowrap;
        padding: 6px 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
        font-weight: 600;
    }

    .nav-link .badge.bg-warning {
        font-size: 0.8rem;
        z-index: 5;
        margin-right: 5px;
        margin-top: 5px;
        padding: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .nav-link[disabled] {
        opacity: 0.7;
        cursor: not-allowed;
    }

    /* Full-width tabs styling */
    .nav-tabs.justify-content-between .nav-item {
        flex: 1;
        text-align: center;
        margin: 0 2px;
    }

    .nav-tabs.justify-content-between .nav-link {
        width: 100%;
        margin-right: 0;
        padding: 12px 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
<div class="container-fluid px-4">
    <!-- Toast Container for Professional Notifications -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
        <!-- Toasts will be dynamically added here -->
    </div>

    <!-- Success notification area at the top (kept for backward compatibility) -->
    <div id="topNotificationArea" class="mb-3" style="display: none;"></div>

    <!-- Empty header - buttons moved to more appropriate locations -->
    <div class="dashboard-header d-flex justify-content-end align-items-center mb-4">
        <div class="d-flex align-items-center">
            <div id="cooldownMessage" class="text-warning me-3" style="display: none;"></div>
        </div>
    </div>

    <!-- Product Category Tabs -->
    <ul class="nav nav-tabs mb-4 d-flex justify-content-between" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active bg-dark text-light" id="tcg-tab" data-bs-toggle="tab" data-bs-target="#tcg-content" type="button" role="tab">
                Trading Card Games
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light" id="boardgames-tab" data-bs-toggle="tab" data-bs-target="#boardgames-content" type="button" role="tab">
                Board Games
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light" id="videogames-tab" data-bs-toggle="tab" data-bs-target="#videogames-content" type="button" role="tab">
                Video Games
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light position-relative" id="warhammer-tab" type="button" disabled>
                Warhammer
                <span class="position-absolute top-0 end-0 badge rounded-pill bg-warning text-dark">
                    <i class="fas fa-hard-hat"></i>
                </span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light position-relative" id="citadel-tab" type="button" disabled>
                Citadel
                <span class="position-absolute top-0 end-0 badge rounded-pill bg-warning text-dark">
                    <i class="fas fa-hard-hat"></i>
                </span>
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link bg-dark text-light position-relative" id="custom-tab" type="button" disabled>
                Custom Items
                <span class="position-absolute top-0 end-0 badge rounded-pill bg-warning text-dark">
                    <i class="fas fa-hard-hat"></i>
                </span>
            </button>
        </li>
    </ul>

    <!-- Settings Accordion -->
    <div class="accordion mb-4" id="settingsAccordion">
        <div class="accordion-item bg-dark border-secondary">
            <h2 class="accordion-header" id="headingSettings">
                <button class="accordion-button collapsed bg-dark text-light border-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSettings" aria-expanded="false" aria-controls="collapseSettings">
                    <i class="fas fa-cog me-2"></i>Settings
                </button>
            </h2>
            <div id="collapseSettings" class="accordion-collapse collapse" aria-labelledby="headingSettings" data-bs-parent="#settingsAccordion">
                <div class="accordion-body">
                    <!-- Settings Header with Queued Files Button -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="mb-0 text-light">Settings</h5>
                        <button id="viewQueuedBtn" class="btn btn-secondary">
                            <i class="fas fa-list me-2"></i>View Queued Files
                        </button>
                    </div>

                    <hr class="border-secondary mb-4">

                    <!-- Title Format Settings -->
                    <h5 class="mb-3 text-light">Title Format Settings</h5>
                    <div class="row g-3 mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-light">Include in Title</label>
                                <div class="d-flex flex-wrap gap-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="includeName" checked disabled>
                                        <label class="form-check-label text-light" for="includeName">Card Name (Required)</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="includeNumber">
                                        <label class="form-check-label text-light" for="includeNumber">Card Number</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="includeExpansion">
                                        <label class="form-check-label text-light" for="includeExpansion">Expansion Name</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label text-light">Component Order (Drag to reorder)</label>
                            <div class="title-components-container p-3 border border-secondary rounded">
                                <div id="titleComponentOrder" class="d-flex gap-2">
                                    <div class="badge bg-primary p-2 draggable cursor-move" data-component="name">Card Name</div>
                                    <div class="badge bg-secondary p-2 draggable cursor-move" data-component="expansion">Expansion Name</div>
                                    <div class="badge bg-secondary p-2 draggable cursor-move" data-component="number">Card Number</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="alert alert-info">
                                <strong>Preview:</strong> <span id="titlePreview" class="ms-2">Opal Palace (352) (CMR)</span>
                            </div>
                        </div>
                        <div class="col-12 text-end">
                             <button id="saveTitleFormatBtn" class="btn btn-primary btn-sm">
                                <i class="fas fa-save me-2"></i>Save Title Settings
                            </button>
                        </div>
                    </div>

                    <hr class="border-secondary">

                    <!-- Condition Selection -->
                    <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0 text-light">Conditions to Include</h5>
                            <button id="saveConditionsBtn" class="btn btn-primary btn-sm">
                                <i class="fas fa-save me-2"></i>Save Condition Settings
                            </button>
                        </div>
                        <div class="d-flex flex-wrap gap-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-NM" value="Near Mint" checked>
                                <label class="form-check-label text-light" for="condition-NM">Near Mint (NM)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-LP" value="Lightly Played" checked>
                                <label class="form-check-label text-light" for="condition-LP">Lightly Played (LP)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-MP" value="Moderately Played" checked>
                                <label class="form-check-label text-light" for="condition-MP">Moderately Played (MP)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-HP" value="Heavily Played" checked>
                                <label class="form-check-label text-light" for="condition-HP">Heavily Played (HP)</label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input condition-checkbox" id="condition-DM" value="Damaged" checked>
                                <label class="form-check-label text-light" for="condition-DM">Damaged (DM)</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Card -->
    <div class="card bg-dark border-secondary mb-4">
        <div class="card-header bg-dark border-secondary">
            <h5 class="mb-0 text-light">Filters</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-2">
                    <label for="gameSelect" class="form-label text-light">Game</label>
                    <select id="gameSelect" class="form-select bg-dark text-light border-secondary">
                        <option value="">Select Game</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="expansionSelect" class="form-label text-light">Expansion</label>
                    <select id="expansionSelect" class="form-select bg-dark text-light border-secondary" disabled>
                        <option value="">Select Expansion</option>
                    </select>
                </div>
                <!-- Filter by Expansion moved to Results Card Header -->
                <div class="col-md-2">
                    <label for="filterSelect" class="form-label text-light">Type</label>
                    <select id="filterSelect" class="form-select bg-dark text-light border-secondary">
                        <option value="all">Show All</option>
                        <option value="singles">Singles Only</option>
                        <option value="sealed">Sealed Only</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="searchInput" class="form-label text-light">Search</label>
                    <input type="text" id="searchInput" class="form-control bg-dark text-light border-secondary" placeholder="Search by name...">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button id="applyFilterBtn" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i>Apply Filters
                    </button>
                </div>
            </div>
            <!-- Condition Selection moved to Settings Accordion -->
        </div>
    </div>

    <!-- Priority Inventory Button -->
    <div class="card bg-dark border-secondary mb-4">
        <div class="card-body text-center">
            <button id="checkInventoryBtn" class="btn btn-success btn-lg">
                <i class="fas fa-boxes me-2"></i>Check My Inventory Priority
            </button>
            <p class="text-muted mt-2 mb-0">Find items you own that aren't yet in your Shopify store</p>
        </div>
    </div>

    <!-- Tab content starts here -->

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- TCG Tab Content -->
        <div class="tab-pane fade show active" id="tcg-content" role="tabpanel">
            <!-- Results Card -->
            <div class="card bg-dark border-secondary">
        <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <h5 class="mb-0 text-light me-3">Results</h5>
                <p id="unmatchedCount" class="mb-0 badge bg-danger me-3" style="display: none;"></p>
            </div>
            <div class="d-flex align-items-center">
                <!-- Filter by Expansion Dropdown -->
                <div class="me-3" style="max-width: 200px;">
                     <label for="resultExpansionFilter" class="form-label text-light visually-hidden">Filter by Expansion</label>
                     <select id="resultExpansionFilter" class="form-select form-select-sm bg-dark text-light border-secondary">
                         <option value="all">Filter by Expansion</option>
                     </select>
                </div>
                <button id="selectAllBtn" class="btn btn-outline-light btn-sm me-3" style="display:none;">
                    <i class="fas fa-check-square me-2"></i>Select All
                </button>
                <button id="pushToUpdatesBtn" class="btn btn-primary btn-sm">
                    <i class="fas fa-cloud-upload-alt me-2"></i>Push Selected to Updates
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div id="unmatchedItemsTableContainer" style="display:none;">
                <div class="table-responsive">
                    <table class="table table-dark table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-secondary" style="width: 50px;">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="selectAllCardsCheckbox">
                                        <label class="form-check-label visually-hidden" for="selectAllCardsCheckbox">Select All Cards</label>
                                    </div>
                                </th>
                                <th class="border-secondary">Name</th>
                                <th class="border-secondary">Expansion</th>
                                <th class="border-secondary">Release Date</th>
                                <th class="border-secondary">Product ID</th>
                            </tr>
                        </thead>
                        <tbody id="unmatchedItemsTableBody">
                            <!-- Items dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div id="paginationContainer" class="d-flex justify-content-center mt-4"></div>

    <!-- Loading Status -->
    <div id="loadingStatus" class="mt-4"></div>
        </div>

        <!-- Board Games Tab Content -->
        <div class="tab-pane fade" id="boardgames-content" role="tabpanel">
            <!-- Search Box -->
            <div class="card bg-dark border-secondary mb-4">
                <div class="card-header bg-dark border-secondary">
                    <h5 class="mb-0 text-light">Search Board Games</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <input type="text" id="boardGameSearchInput" class="form-control bg-dark text-light border-secondary" placeholder="Search for board games by name...">
                        </div>
                        <div class="col-md-4">
                            <button id="boardGameSearchBtn" class="btn btn-primary w-100">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Card -->
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">Board Games Results</h5>
                        <span id="boardGameResultsCount" class="badge bg-info" style="display: none;"></span>
                    </div>
                    <div class="d-flex align-items-center">
                        <button id="selectAllBoardGamesBtn" class="btn btn-outline-light btn-sm me-3" style="display:none;">
                            <i class="fas fa-check-square me-2"></i>Select All
                        </button>
                        <button id="pushBoardGamesToShopifyBtn" class="btn btn-success btn-sm me-3" style="display:none;">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Push to Shopify
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="boardGameResultsContainer" style="display:none;">
                        <div class="table-responsive">
                            <table class="table table-dark table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-secondary" style="width: 50px;">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="selectAllBoardGamesCheckbox">
                                                <label class="form-check-label visually-hidden" for="selectAllBoardGamesCheckbox">Select All</label>
                                            </div>
                                        </th>
                                        <th class="border-secondary" style="width: 80px;">Image</th>
                                        <th class="border-secondary">Title</th>
                                        <th class="border-secondary">UPC/BGG ID</th>
                                        <th class="border-secondary">Year</th>
                                        <th class="border-secondary">Players</th>
                                        <th class="border-secondary">Time</th>
                                    </tr>
                                </thead>
                                <tbody id="boardGameResultsTableBody">
                                    <!-- Board game results will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="boardGameNoResults" class="text-center text-light p-4">
                        <i class="fas fa-search me-2"></i>Enter a search term above to find board games
                    </div>
                </div>
            </div>

            <!-- Pagination for Board Games -->
            <div id="boardGamePaginationContainer" class="d-flex justify-content-center mt-4"></div>
        </div>

        <!-- Video Games Tab Content -->
        <div class="tab-pane fade" id="videogames-content" role="tabpanel">
            <!-- Search Video Games -->
            <div class="card bg-dark border-secondary mb-4">
                <div class="card-header bg-dark border-secondary">
                    <h5 class="mb-0 text-light">
                        <i class="fas fa-gamepad me-2"></i>Search Video Games
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Video Game Filters -->
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="platformFilter" class="form-label text-light">Platform</label>
                            <select class="form-select bg-dark text-light border-secondary" id="platformFilter">
                                <option value="">All Platforms</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="genreFilter" class="form-label text-light">Genre</label>
                            <select class="form-select bg-dark text-light border-secondary" id="genreFilter" disabled>
                                <option value="">All Genres</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="videoGameSearchInput" class="form-label text-light">Search Title</label>
                            <input type="text" class="form-control bg-dark text-light border-secondary"
                                   id="videoGameSearchInput" placeholder="Search by game title..."
                                   onkeypress="if(event.key==='Enter') performVideoGameFilteredSearch()">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-light">&nbsp;</label>
                            <button class="btn btn-primary w-100" type="button" id="searchVideoGamesBtn" onclick="performVideoGameFilteredSearch()">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="videoGameLoadingState" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <h6 class="text-light">Loading video games...</h6>
                        <p class="text-muted small">Please wait while we fetch the results</p>
                    </div>
                </div>
            </div>

            <!-- Video Games Results -->
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">
                            <i class="fas fa-gamepad me-2"></i>Video Games Results
                        </h5>
                        <span id="videoGameResultsCount" class="badge bg-primary">0 results</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <button id="pushVideoGamesToShopifyBtn" class="btn btn-success btn-sm me-3" style="display:none;">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Push to Shopify
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div id="videoGameSearchPrompt" class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Use the search above to find video games. Search by title, platform, or genre.
                    </div>

                    <div id="videoGameResultsContainer" style="display: none;">
                        <!-- Currency Conversion Notice -->
                        <div class="alert alert-info mb-3" id="videoGamesCurrencyNotice">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Currency Notice:</strong> All prices are automatically converted to your store currency (<span id="videoGamesCurrencyCode">USD</span>) and will be pushed to Shopify in this currency.
                        </div>

                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="selectAllVideoGamesCheckbox">
                                                <label class="form-check-label" for="selectAllVideoGamesCheckbox">
                                                    <span class="visually-hidden">Select All</span>
                                                </label>
                                            </div>
                                        </th>
                                        <th style="width: 80px;">Image</th>
                                        <th>Title</th>
                                        <th>Platform</th>
                                        <th>Genre</th>
                                        <th>Year</th>
                                        <th>New/Sealed</th>
                                        <th>CIB</th>
                                        <th>Loose</th>
                                        <th>Box Only</th>
                                        <th>Manual Only</th>
                                        <th>UPC</th>
                                    </tr>
                                </thead>
                                <tbody id="videoGameResultsTableBody">
                                    <!-- Video game results will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div id="videoGameNoResults" style="display: none;" class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No video games found matching your search criteria.
                    </div>
                </div>
            </div>

            <!-- Pagination for Video Games -->
            <div id="videoGamePaginationContainer" class="d-flex justify-content-center mt-4"></div>
        </div>

        <!-- Warhammer Tab Content -->
        <div class="tab-pane fade" id="warhammer-content" role="tabpanel">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">Warhammer</h5>
                    </div>
                    <div class="d-flex align-items-center">
                        <button id="pushToWarhammerBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Push Selected to Updates
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Select a game from the filters above to view Warhammer items.
                    </div>
                </div>
            </div>
        </div>

        <!-- Citadel Tab Content -->
        <div class="tab-pane fade" id="citadel-content" role="tabpanel">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">Citadel</h5>
                    </div>
                    <div class="d-flex align-items-center">
                        <button id="pushToCitadelBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Push Selected to Updates
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Select a game from the filters above to view Citadel items.
                    </div>
                </div>
            </div>
        </div>

        <!-- Custom Items Tab Content -->
        <div class="tab-pane fade" id="custom-content" role="tabpanel">
            <div class="card bg-dark border-secondary">
                <div class="card-header bg-dark border-secondary d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 text-light me-3">Custom Items</h5>
                    </div>
                    <div class="d-flex align-items-center">
                        <button id="pushToCustomBtn" class="btn btn-primary btn-sm">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Push Selected to Updates
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Select a custom item type from the filters above to view custom items.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Queued Files Modal -->
<div class="modal fade" id="queuedFilesModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="fas fa-list me-2"></i>Queued Files
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-dark table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-secondary">File Name</th>
                                <th class="border-secondary">Size</th>
                                <th class="border-secondary">Created</th>
                                <th class="border-secondary">Modified</th>
                            </tr>
                        </thead>
                        <tbody id="queuedFilesTableBody">
                            <!-- Files dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
                <div id="noQueuedFiles" class="text-center text-light p-4" style="display: none;">
                    <i class="fas fa-info-circle me-2"></i>No queued files found
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">Feedback</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="feedbackModalBody">
                <!-- Feedback message inserted here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="z-index: 9999;">
    <div class="position-absolute top-50 start-50 translate-middle text-center text-light">
        <div class="spinner-border mb-3" style="width: 3.5rem; height: 3.5rem; border-width: 0.25rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <h5 id="loadingOverlayText" class="mb-2">Generating Files...</h5>
        <p class="text-muted" style="font-size: 14px;">This may take a few moments</p>
    </div>
</div>

<!-- Language Selection Modal -->
<div class="modal fade" id="languageSelectionModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="fas fa-language me-2"></i>Select Languages
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="languageCheckboxes" class="d-flex flex-column gap-2">
                    <!-- Language checkboxes will be dynamically inserted here -->
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmLanguagesBtn">Continue</button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                    <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                    Error
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="errorModalBody">
                <!-- Error message inserted here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content bg-dark border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title text-light">
                     <i class="fas fa-question-circle me-2"></i>Confirm Action
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-light" id="confirmationModalBody">
                <!-- Confirmation details will be inserted here -->
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmPushBtn">Confirm Push</button>
            </div>
        </div>
    </div>
</div>

<!-- Video Game Details Modal -->
<div class="modal fade" id="videoGameDetailsModal" tabindex="-1" aria-labelledby="videoGameDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark text-light border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="videoGameDetailsModalLabel">
                    <i class="fas fa-gamepad me-2"></i>Video Game Details & Variants
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Game Info Section -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <img id="modalGameImage" src="" alt="" class="img-fluid rounded"
                             style="max-height: 200px; width: 100%; object-fit: cover;"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNGE1NTY4Ii8+CjxwYXRoIGQ9Ik04MCA4MEgxMjBWMTIwSDgwVjgwWiIgZmlsbD0iI2EwYWVjMCIvPgo8cGF0aCBkPSJNOTAgOTBIMTEwVjExMEg5MFY5MFoiIGZpbGw9IiMyZDM3NDgiLz4KPC9zdmc+Cg=='">
                    </div>
                    <div class="col-md-9">
                        <h4 id="modalGameTitle" class="mb-2">Game Title</h4>
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <strong>Platform:</strong> <span id="modalGamePlatform" class="badge bg-info ms-1">Platform</span>
                            </div>
                            <div class="col-sm-6">
                                <strong>Genre:</strong> <span id="modalGameGenre" class="badge bg-secondary ms-1">Genre</span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <strong>Developer:</strong> <span id="modalGameDeveloper">Developer</span>
                            </div>
                            <div class="col-sm-6">
                                <strong>Publisher:</strong> <span id="modalGamePublisher">Publisher</span>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <strong>Release Date:</strong> <span id="modalGameReleaseDate">Release Date</span>
                            </div>
                            <div class="col-sm-6">
                                <strong>UPC:</strong> <span id="modalGameUPC">UPC</span>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Players:</strong> <span id="modalGamePlayers">Players</span>
                            </div>
                            <div class="col-sm-6">
                                <strong>Disc Count:</strong> <span id="modalGameDiscCount">Disc Count</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Description Section -->
                <div class="mb-4">
                    <h6>Description:</h6>
                    <p id="modalGameDescription" class="text-muted small">Game description will appear here...</p>
                </div>

                <!-- Price Variants Section -->
                <div class="mb-4">
                    <h6>Price Variants (Highest to Lowest) <span id="modalCurrencyInfo" class="text-muted small"></span>:</h6>
                    <div class="table-responsive">
                        <table class="table table-dark table-sm">
                            <thead>
                                <tr>
                                    <th>
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAllVariants">
                                            <label class="form-check-label" for="selectAllVariants">Select All</label>
                                        </div>
                                    </th>
                                    <th>Condition</th>
                                    <th>Price</th>
                                </tr>
                            </thead>
                            <tbody id="modalGameVariants">
                                <!-- Variants will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Close
                </button>
                <button type="button" class="btn btn-success" id="pushSelectedVariantsBtn" style="display:none;">
                    <i class="fas fa-cloud-upload-alt me-2"></i>Push Selected Variants to Shopify
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Board Game Price & Quantity Modal -->
<div class="modal fade" id="boardGamePriceModal" tabindex="-1" aria-labelledby="boardGamePriceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content bg-dark text-light border-secondary">
            <div class="modal-header border-secondary">
                <h5 class="modal-title" id="boardGamePriceModalLabel">
                    <i class="fas fa-tag me-2"></i>Set Price & Quantity
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <span id="modalGameInfo">Set the price and quantity for this board game before pushing to Shopify.</span>
                </div>

                <div id="currentGameDisplay" class="mb-3 p-3 border border-secondary rounded" style="background-color: rgba(255, 255, 255, 0.05);">
                    <div class="d-flex align-items-center">
                        <img id="currentGameImage" src="" alt="" class="me-3"
                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;"
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0zMCAxOEMyNS4wMjk0IDE4IDIwLjI1ODEgMTkuOTc1MyAxNi43NTc0IDIzLjQ3NTdDMTMuMjU2OCAyNi45NzYyIDExLjI4MTUgMzEuNzQ4MiAxMS4yODE1IDM2LjcxODhIMTguNzE4NkMxOC43MTg2IDMzLjc0MzcgMTkuOTAyNCAzMC44OTAxIDIyLjAwMjkgMjguNzg5NkMyNC4xMDM0IDI2LjY4OTEgMjYuOTU2OSAyNS41MDU0IDMwIDI1LjUwNTRWMThaIiBmaWxsPSIjNkI3Mjg4Ii8+CjxwYXRoIGQ9Ik0zMCAxOFYyNS41MDU0QzMzLjA0MzEgMjUuNTA1NCAzNS44OTY2IDI2LjY4OTEgMzguMDAwMSAyOC43ODk2QzQwLjEwMzYgMzAuODkwMSA0MS4yODc0IDMzLjc0MzcgNDEuMjg3NCAzNi43MTg4SDQ4LjcxODVDNDguNzE4NSAzMS43NDgyIDQ2Ljc0MzIgMjYuOTc2MiA0My4yNDI2IDIzLjQ3NTdDMzkuNzQxOSAxOS45NzUzIDM0Ljk3MDYgMTggMzAgMThaIiBmaWxsPSIjNkI3Mjg4Ii8+Cjwvc3ZnPgo='">
                        <div class="flex-grow-1">
                            <div class="fw-bold" id="currentGameName">Game Name</div>
                            <small class="text-muted" id="currentGameYear"></small>
                            <div class="mt-1">
                                <span class="badge bg-primary" id="currentGameProgress">Game 1 of 1</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="boardGamePrice" class="form-label">
                            <i class="fas fa-tag me-1"></i>Price
                        </label>
                        <input type="number" class="form-control" id="boardGamePrice"
                               placeholder="0.00" step="0.01" min="0" required>
                        <div class="form-text">Selling price per item</div>
                    </div>
                    <div class="col-md-6">
                        <label for="boardGameQuantity" class="form-label">
                            <i class="fas fa-boxes me-1"></i>Quantity
                        </label>
                        <input type="number" class="form-control" id="boardGameQuantity"
                               placeholder="1" min="0" value="1" required>
                        <div class="form-text">Number of copies available</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-secondary">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-outline-light" id="skipCurrentGame" style="display:none;">
                    <i class="fas fa-forward me-2"></i>Skip This Game
                </button>
                <button type="button" class="btn btn-success" id="confirmPushToShopify">
                    <i class="fas fa-cloud-upload-alt me-2"></i><span id="pushButtonText">Push to Shopify</span>
                </button>
            </div>
        </div>
    </div>
</div>


<!-- Include SortableJS before other scripts -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.14.0/Sortable.min.js"></script>

<style>
.cursor-move {
    cursor: move !important;
}

.title-components-container {
    min-height: 50px;
    background-color: rgba(255, 255, 255, 0.05);
}

.draggable {
    user-select: none;
    touch-action: none;
}

.sortable-ghost {
    opacity: 0.4;
}

.sortable-chosen {
    opacity: 0.8;
    background-color: #e91e63 !important;
}

.sortable-drag {
    opacity: 0.8;
}

#titleComponentOrder {
    min-height: 38px;
}

.badge.draggable {
    font-size: 0.9rem;
    padding: 8px 12px;
}

/* Modal styling */
.modal-content {
    background-color: #1e293b;
    color: #ffffff;
    border: none;
    border-radius: 16px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
}

.modal-header .modal-title {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 18px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 15px 20px;
}

/* Loading overlay styling */
#loadingOverlay {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

#loadingOverlay .spinner-border {
    color: #e91e63 !important;
}

#loadingOverlayText {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    margin-top: 15px;
}

/* Pagination styling */
.pagination {
    margin-top: 20px;
}

.pagination .page-link {
    background-color: #1e293b;
    border-color: #4a5568;
    color: #ffffff;
    transition: all 0.3s ease;
    margin: 0 3px;
    border-radius: 8px;
}

.pagination .page-link:hover {
    background-color: #2d3748;
    border-color: #e91e63;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.pagination .page-item.active .page-link {
    background-color: #e91e63;
    border-color: #e91e63;
}

.pagination .page-item.disabled .page-link {
    background-color: #1a202c;
    border-color: #4a5568;
    color: #6c757d;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize subscription check variables
    let userSubscriptionType = null;
    let isLimitedSubscription = false;
    const MAX_SELECTIONS_FOR_LIMITED_USERS = 10;

    // Cooldown variables removed

    // Function to format release date
    function formatReleaseDate(dateString) {
        if (!dateString) return 'N/A';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'N/A';

            // Format date as MM/DD/YYYY
            return date.toLocaleDateString();
        } catch (error) {
            console.error('Error formatting date:', error);
            return 'N/A';
        }
    }

    // Load saved conditions
    loadSavedConditions();

    // Save conditions button click handler
    document.getElementById('saveConditionsBtn').addEventListener('click', function() {
        saveConditions();
    });

    function loadSavedConditions() {
        fetch('/shopify/update-catalog/api/conditions')
            .then(response => response.json())
            .then(settings => {
                if (settings && settings.selected_conditions) {
                    // Uncheck all conditions first
                    document.querySelectorAll('.condition-checkbox').forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    // Check the saved conditions
                    settings.selected_conditions.forEach(condition => {
                        const checkbox = document.querySelector(`.condition-checkbox[value="${condition}"]`);
                        if (checkbox) {
                            checkbox.checked = true;
                        }
                    });

                    console.log('Loaded saved conditions:', settings.selected_conditions);
                }
            })
            .catch(error => {
                console.error('Error loading saved conditions:', error);
            });
    }

    function saveConditions() {
        const selectedConditions = Array.from(document.querySelectorAll('.condition-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedConditions.length === 0) {
            showErrorMessage('Please select at least one condition.');
            return;
        }

        const settings = {
            selected_conditions: selectedConditions
        };

        fetch('/shopify/update-catalog/api/conditions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFeedbackMessage('Conditions settings saved successfully', 'success');
            } else {
                showFeedbackMessage('Failed to save conditions settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving conditions settings:', error);
            showFeedbackMessage('Failed to save conditions settings', 'error');
        });
    }

    // Fetch user subscription info
    function fetchUserSubscriptionInfo() {
        fetch('/api/user/subscription-info')
            .then(response => response.json())
            .then(data => {
                userSubscriptionType = data.subscriptionName;
                // Apply limit only if the user is on a 'Free' subscription (case-insensitive)
                // Make sure we're not limiting users on Monthly, Annual, or Lifetime packages
                isLimitedSubscription = userSubscriptionType && userSubscriptionType.toLowerCase() === 'free';

                // Fix for Monthly Basic users - they should not be limited
                if (userSubscriptionType &&
                    (userSubscriptionType.toLowerCase().includes('monthly') ||
                     userSubscriptionType.toLowerCase().includes('annual') ||
                     userSubscriptionType.toLowerCase().includes('lifetime'))) {
                    isLimitedSubscription = false;
                }

                console.log("User subscription type:", userSubscriptionType);
                console.log("Is limited subscription:", isLimitedSubscription);

                // Display subscription warning if needed
                if (isLimitedSubscription) {
                    const warningDiv = document.createElement('div');
                    warningDiv.className = 'alert alert-warning mb-4';
                    warningDiv.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Subscription Notice:</strong> Your current subscription (${userSubscriptionType})
                        only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time.
                        <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.
                    `;

                    // Insert after the header
                    const header = document.querySelector('.dashboard-header');
                    header.parentNode.insertBefore(warningDiv, header.nextSibling);
                }
            })
            .catch(error => {
                console.error('Error fetching subscription info:', error);
            });
    }

    // Call the function to fetch subscription info
    fetchUserSubscriptionInfo();
    // Smoshey records functionality removed as we only work with catalog items

    // Smoshey records functionality removed as we only work with catalog items

    // Initialize missing product IDs
    let missingProductIds = [];
    let allProducts = [];

    // Staged items functionality removed as we only work with catalog items

    // Staged items functionality removed as we only work with catalog items
    // Sample card data for preview
    const previewCard = {
        name: 'Opal Palace',
        expansionName: 'Commander Legends',
        number: '352'
    };

    // Call updateTitlePreview immediately to ensure the preview is updated
    setTimeout(updateTitlePreview, 100);

    // Initialize Sortable with animation and proper handling
    new Sortable(document.getElementById('titleComponentOrder'), {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        forceFallback: true,
        fallbackClass: 'sortable-fallback',
        onEnd: function(evt) {
            updateTitlePreview();
            // Update visual state of badges
            updateBadgeStates();
        }
    });

    // Load saved title format settings
    fetch('/shopify/update-catalog/api/title-format')
        .then(response => response.json())
        .then(settings => {
            document.getElementById('includeNumber').checked = settings.includeNumber;
            document.getElementById('includeExpansion').checked = settings.includeExpansion;

        // Update component order
        const orderContainer = document.getElementById('titleComponentOrder');
        orderContainer.innerHTML = '';
        settings.order.forEach(component => {
            // Map 'abbreviation' to 'expansion' for backwards compatibility
            const mappedComponent = component === 'abbreviation' ? 'expansion' : component;

            const div = document.createElement('div');
            const isEnabled = mappedComponent === 'name' ||
                            (mappedComponent === 'number' && settings.includeNumber) ||
                            (mappedComponent === 'expansion' && settings.includeExpansion);

            div.className = `badge ${isEnabled ? 'bg-primary' : 'bg-secondary'} p-2 draggable cursor-move`;
            div.dataset.component = mappedComponent;
            div.textContent = mappedComponent === 'name' ? 'Card Name' :
                            mappedComponent === 'number' ? 'Card Number' : 'Expansion Name';
            orderContainer.appendChild(div);
        });

            updateTitlePreview();
        })
        .catch(error => {
            console.error('Error loading title format settings:', error);
            updateTitlePreview(); // Still show preview with defaults
        });

    function updateBadgeStates() {
        document.querySelectorAll('#titleComponentOrder .draggable').forEach(badge => {
            const type = badge.dataset.component;
            const isEnabled = type === 'name' ||
                            (type === 'number' && document.getElementById('includeNumber').checked) ||
                            (type === 'expansion' && document.getElementById('includeExpansion').checked);
            badge.className = `badge ${isEnabled ? 'bg-primary' : 'bg-secondary'} p-2 draggable cursor-move`;
        });
    }

    // Handle checkbox changes
    document.getElementById('includeNumber').addEventListener('change', function() {
        updateBadgeStates();
        updateTitlePreview();
    });

    document.getElementById('includeExpansion').addEventListener('change', function() {
        updateBadgeStates();
        updateTitlePreview();
    });

    // Save title format settings
    document.getElementById('saveTitleFormatBtn').addEventListener('click', function() {
        const settings = {
            includeName: true,
            includeNumber: document.getElementById('includeNumber').checked,
            includeExpansion: document.getElementById('includeExpansion').checked,
            order: Array.from(document.querySelectorAll('#titleComponentOrder .draggable'))
                      .map(el => el.dataset.component)
        };

        fetch('/shopify/update-catalog/api/title-format', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(settings)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showFeedbackMessage('Title format settings saved successfully', 'success');
            } else {
                showFeedbackMessage('Failed to save title format settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error saving title format settings:', error);
            showFeedbackMessage('Failed to save title format settings', 'error');
        });
    });

    function updateTitlePreview() {
        const preview = [];
        const components = document.querySelectorAll('#titleComponentOrder .draggable');

        components.forEach(component => {
            const type = component.dataset.component;
            const isEnabled = type === 'name' ||
                            (type === 'number' && document.getElementById('includeNumber').checked) ||
                            (type === 'expansion' && document.getElementById('includeExpansion').checked);

            if (isEnabled) {
                switch (type) {
                    case 'name':
                        preview.push(previewCard.name);
                        break;
                    case 'number':
                        preview.push(`(${previewCard.number})`);
                        break;
                    case 'expansion':
                        preview.push(`(${previewCard.expansionName})`);
                        break;
                }
            }
        });

        document.getElementById('titlePreview').textContent = preview.join(' ');

        // Log for debugging
        console.log('Preview components:', preview);
        console.log('Include Number:', document.getElementById('includeNumber').checked);
        console.log('Include Expansion:', document.getElementById('includeExpansion').checked);
        console.log('Component order:', Array.from(components).map(c => c.dataset.component));
    }

    function showFeedbackMessage(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-circle';

        const feedbackModal = new bootstrap.Modal(document.getElementById('feedbackModal'));
        document.getElementById('feedbackModalBody').innerHTML = `
            <div class="alert ${alertClass} mb-0">
                <i class="fas fa-${icon} me-2"></i>${message}
            </div>
        `;
        feedbackModal.show();
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize queued files modal
    const queuedFilesModal = new bootstrap.Modal(document.getElementById('queuedFilesModal'));

    document.getElementById('viewQueuedBtn').addEventListener('click', function() {
        fetchQueuedFiles();
    });

    function fetchQueuedFiles() {
        fetch('/shopify/update-catalog/api/queued-files')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('queuedFilesTableBody');
                const noFiles = document.getElementById('noQueuedFiles');

                tableBody.innerHTML = '';

                if (data.files && data.files.length > 0) {
                    data.files.forEach(file => {
                        const row = document.createElement('tr');
                        const size = formatFileSize(file.size);
                        const created = new Date(file.created).toLocaleString();
                        const modified = new Date(file.modified).toLocaleString();

                        row.innerHTML = `
                            <td class="border-secondary">${file.name}</td>
                            <td class="border-secondary">${size}</td>
                            <td class="border-secondary">${created}</td>
                            <td class="border-secondary">${modified}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                    tableBody.closest('.table-responsive').style.display = 'block';
                    noFiles.style.display = 'none';
                } else {
                    tableBody.closest('.table-responsive').style.display = 'none';
                    noFiles.style.display = 'block';
                }

                queuedFilesModal.show();
            })
            .catch(error => {
                console.error('Error fetching queued files:', error);
                showErrorMessage('Failed to load queued files. Please try again.');
            });
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    fetch('/shopify/update-catalog/api/games')
        .then(response => response.json())
        .then(games => {
            const gameSelect = document.getElementById('gameSelect');
            games.forEach(game => {
                const option = document.createElement('option');
                option.value = game;
                option.textContent = game;
                gameSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error fetching games:', error);
            showErrorMessage('Failed to load games. Please refresh the page or contact support.');
        });

    function fetchAndPopulateFilters() {
        fetch('/shopify/update-catalog/api/unmatched-items')
            .then(response => response.json())
            .then(data => {
                if (data && data.filters) {
                    const expansionSelect = document.getElementById('expansionSelect');
                    expansionSelect.innerHTML = '<option value="all">All Expansions</option>';
                    data.filters.expansionNames.forEach(expansion => {
                        const option = document.createElement('option');
                        option.value = expansion;
                        option.textContent = expansion;
                        expansionSelect.appendChild(option);
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching filters:', error);
                showErrorMessage('Failed to load filters. Please refresh the page or contact support.');
            });
    }

    fetchAndPopulateFilters();

    document.getElementById('gameSelect').addEventListener('change', function() {
        const gameName = this.value;
        const expansionSelect = document.getElementById('expansionSelect');
        expansionSelect.innerHTML = '<option value="">Select Expansion</option>';
        document.getElementById('unmatchedCount').style.display = 'none';
        document.getElementById('unmatchedItemsTableContainer').style.display = 'none';

        if (gameName) {
            expansionSelect.disabled = true;
            expansionSelect.innerHTML = '<option value="">Loading...</option>';

            fetch(`/shopify/update-catalog/api/expansions?gameName=${encodeURIComponent(gameName)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(expansions => {
                    expansionSelect.innerHTML = '<option value="">Select Expansion</option>';
                    expansions.forEach(expansion => {
                        const option = document.createElement('option');
                        option.value = expansion;
                        option.textContent = expansion;
                        expansionSelect.appendChild(option);
                    });
                    expansionSelect.disabled = false;
                })
                .catch(error => {
                    console.error('Error fetching expansions:', error);
                    expansionSelect.innerHTML = '<option value="">Error loading expansions</option>';
                    showErrorMessage('Failed to load expansions. Please try again.');
                });
        } else {
            expansionSelect.disabled = true;
        }
    });

    document.getElementById('searchInput').addEventListener('keyup', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('applyFilterBtn').click();
        }
    });

document.getElementById('applyFilterBtn').addEventListener('click', function() {
        const gameName = document.getElementById('gameSelect').value;
        const expansionName = document.getElementById('expansionSelect').value;
        const filter = getSelectedFilter();
        const searchTerm = document.getElementById('searchInput').value.trim();

        // Show loading overlay with appropriate text
        document.getElementById('loadingOverlayText').textContent = "Processing Filters...";
        showLoadingOverlay();

        fetchUnmatchedItems(gameName, expansionName, filter, null, null, 1, searchTerm);
    });

    let currentPage = 1;
    let totalPages = 1;

    function updateResultExpansionFilter(products) {
        const expansionFilter = document.getElementById('resultExpansionFilter');
        const currentValue = expansionFilter.value;

        const expansions = [...new Set(products.map(p => p.expansionName))].filter(e => e).sort();

        expansionFilter.innerHTML = '<option value="all">All Expansions</option>';
        expansions.forEach(expansion => {
            const option = document.createElement('option');
            option.value = expansion;
            option.textContent = expansion;
            expansionFilter.appendChild(option);
        });

        if (expansions.includes(currentValue)) {
            expansionFilter.value = currentValue;
        }
    }

    function filterAndDisplayProducts(products) {
        const expansionFilter = document.getElementById('resultExpansionFilter');
        const selectedExpansion = expansionFilter.value;

        let filteredProducts = products;
        if (selectedExpansion !== 'all') {
            filteredProducts = products.filter(p => p.expansionName === selectedExpansion);
        }

        displayProducts(filteredProducts);
    }

    // Function to check if selection limit is reached for limited subscription users
    function checkSelectionLimit() {
        if (!isLimitedSubscription) return true; // No limit for premium subscriptions

        const catalogProducts = document.querySelectorAll('.productCheckbox:checked').length;
        return catalogProducts <= MAX_SELECTIONS_FOR_LIMITED_USERS;
    }

    // Function to enforce selection limit
    function enforceSelectionLimit(checkbox) {
        if (isLimitedSubscription && checkbox.checked) {
            const catalogProducts = document.querySelectorAll('.productCheckbox:checked').length;

            if (catalogProducts > MAX_SELECTIONS_FOR_LIMITED_USERS) {
                checkbox.checked = false;
                const upgradeAction = [{
                    text: 'Upgrade Now',
                    onclick: 'window.open("/activating", "_blank")'
                }];
                showWarningToast(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. Upgrade to select more items.`, 8000, upgradeAction);
                return false;
            }
        }
        return true;
    }

    function displayProducts(products) {
        const unmatchedCount = document.getElementById('unmatchedCount');
        const unmatchedItemsTableBody = document.getElementById('unmatchedItemsTableBody');
        unmatchedItemsTableBody.innerHTML = '';

        if (products.length > 0) {
            unmatchedCount.textContent = `${products.length} items`;
            unmatchedCount.style.display = 'inline-block';

            // First sort by release date (newest first)
            products.sort((a, b) => {
                // Handle missing products first
                const aIsMissing = missingProductIds.includes(a.productId);
                const bIsMissing = missingProductIds.includes(b.productId);
                if (aIsMissing && !bIsMissing) return -1;
                if (!aIsMissing && bIsMissing) return 1;

                // Then sort by release date (newest first)
                // Handle cases where release date might be missing
                if (!a.releasedOn && !b.releasedOn) return 0;
                if (!a.releasedOn) return 1; // Items without dates go last
                if (!b.releasedOn) return -1;

                // Compare dates - future dates should be considered "newest"
                const dateA = new Date(a.releasedOn);
                const dateB = new Date(b.releasedOn);

                // Check if dates are valid
                const isValidDateA = !isNaN(dateA.getTime());
                const isValidDateB = !isNaN(dateB.getTime());

                if (!isValidDateA && !isValidDateB) return 0;
                if (!isValidDateA) return 1;
                if (!isValidDateB) return -1;

                // Sort newest first
                return dateB - dateA;
            });

            products.forEach(product => {
                const row = document.createElement('tr');
                const isMissing = missingProductIds.includes(product.productId);
                row.innerHTML = `
                    <td class="align-middle border-secondary">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input productCheckbox" id="checkbox-${product.productId}"
                                   data-product-id="${product.productId}" ${isMissing ? 'checked' : ''}>
                            <label class="form-check-label" for="checkbox-${product.productId}"></label>
                        </div>
                    </td>
                    <td class="align-middle border-secondary">
                        ${product.name}
                        ${isMissing ? '<span class="badge bg-warning text-dark missing-badge">Missing</span>' : ''}
                    </td>
                    <td class="align-middle border-secondary">${product.expansionName || 'N/A'}</td>
                    <td class="align-middle border-secondary">${formatReleaseDate(product.releasedOn)}</td>
                    <td class="align-middle border-secondary">${product.productId}</td>
                `;
                unmatchedItemsTableBody.appendChild(row);

                // Add event listener to enforce selection limit
                const checkbox = row.querySelector('.productCheckbox');
                checkbox.addEventListener('change', function() {
                    enforceSelectionLimit(this);
                });
            });

            document.getElementById('unmatchedItemsTableContainer').style.display = 'block';
            document.getElementById('selectAllBtn').style.display = 'inline-block';
        } else {
            unmatchedCount.textContent = '0 items';
            unmatchedCount.classList.remove('bg-danger');
            unmatchedCount.classList.add('bg-success');
            unmatchedCount.style.display = 'inline-block';
            document.getElementById('unmatchedItemsTableContainer').style.display = 'none';
            document.getElementById('selectAllBtn').style.display = 'none';
        }
    }

    // Function to display priority products (similar to displayProducts but with priority badges)
    function displayPriorityProducts(products) {
        const unmatchedCount = document.getElementById('unmatchedCount');
        const unmatchedItemsTableBody = document.getElementById('unmatchedItemsTableBody');
        unmatchedItemsTableBody.innerHTML = '';

        if (products.length > 0) {
            unmatchedCount.textContent = `${products.length} priority items`;
            unmatchedCount.classList.remove('bg-danger');
            unmatchedCount.classList.add('bg-info');
            unmatchedCount.style.display = 'inline-block';

            // Sort by release date (newest first)
            products.sort((a, b) => {
                if (!a.releasedOn && !b.releasedOn) return 0;
                if (!a.releasedOn) return 1;
                if (!b.releasedOn) return -1;

                const dateA = new Date(a.releasedOn);
                const dateB = new Date(b.releasedOn);

                const isValidDateA = !isNaN(dateA.getTime());
                const isValidDateB = !isNaN(dateB.getTime());

                if (!isValidDateA && !isValidDateB) return 0;
                if (!isValidDateA) return 1;
                if (!isValidDateB) return -1;

                return dateB - dateA;
            });

            products.forEach(product => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="align-middle border-secondary">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input productCheckbox" id="checkbox-${product.productId}"
                                   data-product-id="${product.productId}" checked>
                            <label class="form-check-label" for="checkbox-${product.productId}"></label>
                        </div>
                    </td>
                    <td class="align-middle border-secondary">
                        ${product.name}
                        <span class="badge bg-info text-dark priority-badge">Priority</span>
                    </td>
                    <td class="align-middle border-secondary">${product.expansionName || 'N/A'}</td>
                    <td class="align-middle border-secondary">${formatReleaseDate(product.releasedOn)}</td>
                    <td class="align-middle border-secondary">${product.productId}</td>
                `;
                unmatchedItemsTableBody.appendChild(row);

                // Add event listener to enforce selection limit
                const checkbox = row.querySelector('.productCheckbox');
                checkbox.addEventListener('change', function() {
                    enforceSelectionLimit(this);
                });
            });

            document.getElementById('unmatchedItemsTableContainer').style.display = 'block';
            document.getElementById('selectAllBtn').style.display = 'inline-block';
            
            // Update select all button text since items are pre-selected
            document.getElementById('selectAllBtn').innerHTML = '<i class="fas fa-square me-2"></i>Deselect All';
        }
    }

    function fetchUnmatchedItems(gameName, expansionName, filter, set, rarity, page = 1, searchTerm = '') {
        let url = `/shopify/update-catalog/api/unmatched-items?gameName=${encodeURIComponent(gameName)}&filter=${filter}&page=${page}`;
        if (expansionName) {
            url += `&expansionName=${encodeURIComponent(expansionName)}`;
        }
        if (searchTerm) {
            url += `&search=${encodeURIComponent(searchTerm)}`;
        }

        const fetchTimeout = 120000;
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timed out')), fetchTimeout);
        });

        Promise.race([
            fetch(url),
            timeoutPromise
        ])
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Hide loading overlay
            hideLoadingOverlay();

            if (data.count > 0) {
                allProducts = data.products;
                updateResultExpansionFilter(data.products);
                filterAndDisplayProducts(data.products);

                currentPage = data.page;
                totalPages = data.total_pages;
                updatePagination();
            } else {
                displayProducts([]);
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide loading overlay
            hideLoadingOverlay();

            let errorMessage = 'An unexpected error occurred. Please try again.';
            if (error.message === 'Request timed out') {
                errorMessage = 'The request timed out. Please try again with more specific filters.';
            }

            showErrorMessage(errorMessage);
        });
    }

    function updatePagination() {
        const paginationContainer = document.getElementById('paginationContainer');
        paginationContainer.innerHTML = '';

        if (totalPages > 1) {
            const nav = document.createElement('nav');
            const ul = document.createElement('ul');
            ul.className = 'pagination';

            // Previous button
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `
                <button class="page-link bg-dark text-light border-secondary" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
            if (currentPage !== 1) {
                prevLi.querySelector('button').addEventListener('click', () => changePage(currentPage - 1));
            }
            ul.appendChild(prevLi);

            // Page numbers
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                const li = document.createElement('li');
                li.className = `page-item ${i === currentPage ? 'active' : ''}`;
                li.innerHTML = `
                    <button class="page-link bg-dark text-light border-secondary">
                        ${i}
                    </button>
                `;
                if (i !== currentPage) {
                    li.querySelector('button').addEventListener('click', () => changePage(i));
                }
                ul.appendChild(li);
            }

            // Next button
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `
                <button class="page-link bg-dark text-light border-secondary" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
            `;
            if (currentPage !== totalPages) {
                nextLi.querySelector('button').addEventListener('click', () => changePage(currentPage + 1));
            }
            ul.appendChild(nextLi);

            nav.appendChild(ul);
            paginationContainer.appendChild(nav);
        }
    }

    function changePage(page) {
        const searchTerm = document.getElementById('searchInput').value.trim();

        // Show loading overlay with appropriate text
        document.getElementById('loadingOverlayText').textContent = "Loading Page...";
        showLoadingOverlay();

        fetchUnmatchedItems(
            document.getElementById('gameSelect').value,
            document.getElementById('expansionSelect').value,
            getSelectedFilter(),
            null,
            null,
            page,
            searchTerm
        );
    }

    function showErrorMessage(message) {
        const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
        document.getElementById('errorModalBody').innerHTML = `
            <div class="alert alert-danger mb-0">
                ${message}
            </div>
        `;
        errorModal.show();
    }


    function getSelectedFilter() {
        return document.getElementById('filterSelect').value;
    }

    document.getElementById('selectAllBtn').addEventListener('click', function() {
        const allChecked = this.innerHTML.includes('Deselect');
        const checkboxes = document.querySelectorAll('.productCheckbox');

        if (!allChecked && isLimitedSubscription) {
            // If selecting all and user has limited subscription, check if it would exceed the limit
            const currentlySelected = document.querySelectorAll('.staged-checkbox:checked').length +
                                     document.querySelectorAll('.smoshey-checkbox:checked').length;

            if (checkboxes.length + currentlySelected > MAX_SELECTIONS_FOR_LIMITED_USERS) {
                // If selecting all would exceed the limit, only select up to the limit
                showErrorMessage(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. Only the first ${MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected} items will be selected. <a href="/activating" class="alert-link">Upgrade to a Monthly, Annual, or Lifetime package</a> to select more items.`);

                // Uncheck all first
                checkboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Then check only up to the limit
                const remainingSlots = MAX_SELECTIONS_FOR_LIMITED_USERS - currentlySelected;
                for (let i = 0; i < Math.min(remainingSlots, checkboxes.length); i++) {
                    checkboxes[i].checked = true;
                }

                // Update button text
                this.innerHTML = '<i class="fas fa-square me-2"></i>Deselect All';
                return;
            }
        }

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        this.innerHTML = allChecked ?
            '<i class="fas fa-check-square me-2"></i>Select All' :
            '<i class="fas fa-square me-2"></i>Deselect All';
    });

    function showLoadingOverlay() {
        document.getElementById('loadingOverlay').classList.remove('d-none');
    }

    function hideLoadingOverlay() {
        document.getElementById('loadingOverlay').classList.add('d-none');
    }

    // Function to fetch unique languages for selected products
    async function fetchUniqueLanguages(selectedProductIds) {
        try {
            const response = await fetch('/shopify/update-catalog/api/unique-languages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ selectedProductIds })
            });

            if (!response.ok) {
                throw new Error('Failed to fetch languages');
            }

            const data = await response.json();
            return data.languages;
        } catch (error) {
            console.error('Error fetching languages:', error);
            throw error;
        }
    }

    // Function to show language selection modal
    function showLanguageSelectionModal(languages, callback) {
        const languageCheckboxes = document.getElementById('languageCheckboxes');
        languageCheckboxes.innerHTML = '';

        languages.forEach(lang => {
            const div = document.createElement('div');
            div.className = 'form-check';
            div.innerHTML = `
                <input type="checkbox" class="form-check-input language-checkbox" id="lang-${lang}" value="${lang}" ${lang === 'EN' ? 'checked' : ''}>
                <label class="form-check-label text-light" for="lang-${lang}">${lang}</label>
            `;
            languageCheckboxes.appendChild(div);
        });

        const modal = new bootstrap.Modal(document.getElementById('languageSelectionModal'));

        // Remove any existing event listener
        const confirmBtn = document.getElementById('confirmLanguagesBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listener
        newConfirmBtn.addEventListener('click', () => {
            const selectedLanguages = Array.from(document.querySelectorAll('.language-checkbox:checked'))
                .map(checkbox => checkbox.value);
            modal.hide();
            callback(selectedLanguages);
        });

        modal.show();
    }

    // Function to check if the push button is on cooldown
    function isPushButtonOnCooldown() {
        // Always return false to disable cooldown
        return false;
    }

    // Function to update cooldown message
    function updateCooldownMessage() {
        const cooldownMessage = document.getElementById('cooldownMessage');
        // Always hide the cooldown message
        cooldownMessage.style.display = 'none';
    }

    // Check cooldown on page load
    updateCooldownMessage();

    // Add event listener for Check My Inventory button
    document.getElementById('checkInventoryBtn').addEventListener('click', function() {
        // Show loading overlay
        document.getElementById('loadingOverlayText').textContent = "Checking Your Inventory...";
        showLoadingOverlay();
        
        // Clear any existing results
        document.getElementById('unmatchedCount').style.display = 'none';
        document.getElementById('unmatchedItemsTableContainer').style.display = 'none';
        
        // Fetch inventory priority items
        fetchInventoryPriorityItems();
    });

    // Function to fetch inventory priority items
    function fetchInventoryPriorityItems() {
        fetch('/shopify/update-catalog/api/inventory-priority-items')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Hide loading overlay
                hideLoadingOverlay();
                
                if (data.count > 0) {
                    allProducts = data.products;
                    updateResultExpansionFilter(data.products);
                    
                    // Mark all items as priority and display them
                    displayPriorityProducts(data.products);
                } else {
                    displayProducts([]);
                    // Show a message that all inventory items are already in Shopify
                    const topNotificationArea = document.getElementById('topNotificationArea');
                    topNotificationArea.innerHTML = `
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="fas fa-check-circle me-2"></i>Great! All your inventory items are already in your Shopify store.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                    topNotificationArea.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                hideLoadingOverlay();
                showErrorMessage('Failed to check inventory. Please try again.');
            });
    }

    document.getElementById('pushToUpdatesBtn').addEventListener('click', async function() {
        // Get selected products from catalog
        const catalogProducts = Array.from(document.querySelectorAll('.productCheckbox:checked'))
            .map(checkbox => parseInt(checkbox.dataset.productId));

        const selectedProducts = [...catalogProducts];
        const missingCount = selectedProducts.filter(id => missingProductIds.includes(id)).length;
        const totalCount = selectedProducts.length;

        if (totalCount === 0) {
            showErrorMessage('Please select at least one product to push to updates.');
            return;
        }

    // Check if user has a limited subscription and has selected too many items
    console.log("Checking selection limit - Subscription type:", userSubscriptionType);
    console.log("Is limited subscription:", isLimitedSubscription);
    console.log("Total selected items:", totalCount);

    if (isLimitedSubscription && totalCount > MAX_SELECTIONS_FOR_LIMITED_USERS) {
        const upgradeAction = [{
            text: 'Upgrade Now',
            onclick: 'window.open("/activating", "_blank")'
        }];
        showWarningToast(`Your current subscription (${userSubscriptionType}) only allows you to select up to ${MAX_SELECTIONS_FOR_LIMITED_USERS} items at a time. Upgrade to select more items.`, 8000, upgradeAction);
        return;
    }

        const selectedConditions = Array.from(document.querySelectorAll('.condition-checkbox:checked'))
            .map(checkbox => checkbox.value);

        if (selectedConditions.length === 0) {
            showErrorMessage('Please select at least one condition.');
            return;
        }

        try {
            // Fetch unique languages for selected products
            const languages = await fetchUniqueLanguages(selectedProducts);

            // Show language selection modal only if languages are available
            if (languages && languages.length > 0) {
                showLanguageSelectionModal(languages, (selectedLanguages) => {
                    if (selectedLanguages.length === 0) {
                        showErrorMessage('Please select at least one language.');
                        return;
                    }

                    // Show custom confirmation modal instead of confirm()
                    showConfirmationModal(totalCount, missingCount, selectedConditions, selectedLanguages, (confirmed) => {
                        if (confirmed) {
                            showLoadingOverlay();
                            const gameName = document.getElementById('gameSelect').value;
                            processUpdate({
                                gameName: gameName,
                                selectedProductIds: selectedProducts,
                                selectedConditions: selectedConditions,
                                selectedLanguages: selectedLanguages
                            });
                        }
                    });
                });
            } else {
                // If no languages available, proceed without language selection but use custom modal
                 showConfirmationModal(totalCount, missingCount, selectedConditions, [], (confirmed) => {
                    if (confirmed) {
                        showLoadingOverlay();
                        const gameName = document.getElementById('gameSelect').value;
                        processUpdate({
                            gameName: gameName,
                            selectedProductIds: selectedProducts,
                            selectedConditions: selectedConditions,
                            selectedLanguages: [] // Pass empty array for languages
                        });
                    }
                });
            }
        } catch (error) {
            showErrorMessage('Failed to fetch languages. Please try again.');
        }
    });

    // Function to show the custom confirmation modal
    function showConfirmationModal(totalCount, missingCount, conditions, languages, callback) {
        const modalBody = document.getElementById('confirmationModalBody');
        const confirmBtn = document.getElementById('confirmPushBtn');
        const confirmationModal = new bootstrap.Modal(document.getElementById('confirmationModal'));

        let languageText = languages.length > 0 ? languages.join(', ') : 'Default (EN)';

        modalBody.innerHTML = `
            <p>You are about to push <strong>${totalCount} product(s)</strong> to updates.</p>
            ${missingCount > 0 ? `<p class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i> Includes ${missingCount} missing item(s).</p>` : ''}
            <hr class="border-secondary">
            <p><strong>Conditions to include:</strong><br>${conditions.join(', ')}</p>
            <p><strong>Languages to include:</strong><br>${languageText}</p>
            <hr class="border-secondary">
            <p>Do you want to proceed?</p>
        `;

        // Remove previous listener to avoid duplicates
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new listener
        newConfirmBtn.addEventListener('click', () => {
            confirmationModal.hide();
            callback(true); // User confirmed
        }, { once: true }); // Ensure listener only fires once

        // Handle modal close/cancel
        const modalElement = document.getElementById('confirmationModal');
        modalElement.addEventListener('hidden.bs.modal', () => {
             // Check if the callback was already called by the confirm button
             // This simple check might not be foolproof in complex scenarios
             // but works here as we only care if confirm was clicked.
             if (!newConfirmBtn.dataset.confirmed) {
                 //callback(false); // User cancelled (removed this line as it caused issues if confirm was clicked quickly)
             }
             // Reset state if needed
             delete newConfirmBtn.dataset.confirmed;
        }, { once: true });

        // Mark confirm button state when clicked
         newConfirmBtn.addEventListener('click', () => {
             newConfirmBtn.dataset.confirmed = 'true';
         }, { once: true });


        confirmationModal.show();
    }


    function processUpdate(data) {
        fetch('/shopify/update-catalog/api/process', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                selectedProductIds: data.selectedProductIds,
                selectedConditions: data.selectedConditions,
                selectedLanguages: data.selectedLanguages
            })
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            const loadingStatus = document.getElementById('loadingStatus');

            if (data.type === 'error') {
                loadingStatus.innerHTML += `
                    <div class="alert alert-danger mt-3">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                    </div>
                `;
            } else if (data.type === 'info') {
                // Show info message at the top
                const topNotificationArea = document.getElementById('topNotificationArea');
                topNotificationArea.innerHTML = `
                    <div class="alert alert-warning alert-dismissible fade show">
                        <i class="fas fa-info-circle me-2"></i>${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                topNotificationArea.style.display = 'block';

                // Also add to loading status for history
                loadingStatus.innerHTML += `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-info-circle me-2"></i>${data.message}
                    </div>
                `;
            } else {
                // Show success message at the top
                const fileCount = data.fileCount || 0;
                const topNotificationArea = document.getElementById('topNotificationArea');
                topNotificationArea.innerHTML = `
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i>${data.message}
                        <button class="btn btn-sm btn-outline-success ms-3" onclick="document.getElementById('viewQueuedBtn').click()">
                            <i class="fas fa-eye me-1"></i>View Files
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                topNotificationArea.style.display = 'block';

                // Cooldown setting removed

                // Also add to loading status for history
                loadingStatus.innerHTML += `
                    <div class="alert alert-success mt-3">
                        <i class="fas fa-check-circle me-2"></i>${data.message}
                    </div>
                `;

                // Automatically open the queued files modal if files were created
                if (fileCount > 0) {
                    setTimeout(() => {
                        document.getElementById('viewQueuedBtn').click();
                    }, 1000);
                }
            }

            if (data.missingProducts?.length > 0) {
                // Add missing products warning to loading status
                loadingStatus.innerHTML += `
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>Missing products: ${data.missingProducts.join(', ')}
                    </div>
                `;
                missingProductIds = data.missingProducts.map(id => parseInt(id));
            }
        })
        .catch(error => {
            console.error('Error:', error);

            // Show error at the top
            const topNotificationArea = document.getElementById('topNotificationArea');
            topNotificationArea.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i>${error.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            topNotificationArea.style.display = 'block';

            // Also add to loading status
            document.getElementById('loadingStatus').innerHTML += `
                <div class="alert alert-danger mt-3">
                    <i class="fas fa-exclamation-circle me-2"></i>${error.message}
                </div>
            `;
        })
        .finally(() => {
            hideLoadingOverlay();
            setTimeout(() => {
                const gameName = document.getElementById('gameSelect').value;
                const expansionName = document.getElementById('expansionSelect').value;
                const filter = getSelectedFilter();
                const searchTerm = document.getElementById('searchInput').value.trim();
                fetchUnmatchedItems(gameName, expansionName, filter, null, null, 1, searchTerm);
            }, 3000);
        });
    }

    document.getElementById('resultExpansionFilter').addEventListener('change', function() {
        filterAndDisplayProducts(allProducts);
    });

    // TCG tab is the only active tab for now
    document.getElementById('tcg-tab')?.addEventListener('click', function() {
        // Reload TCG data if needed
        console.log('TCG tab clicked');
    });

    // Board Games Search Functionality
    let currentBoardGamePage = 1;
    let boardGameSearchQuery = '';

    function searchBoardGames(query, page = 1) {
        if (!query || query.trim() === '') {
            document.getElementById('boardGameResultsContainer').style.display = 'none';
            document.getElementById('boardGameNoResults').innerHTML = '<i class="fas fa-search me-2"></i>Enter a search term above to find board games';
            document.getElementById('boardGameResultsCount').style.display = 'none';
            document.getElementById('selectAllBoardGamesBtn').style.display = 'none';
            document.getElementById('pushBoardGamesToShopifyBtn').style.display = 'none';
            document.getElementById('boardGamePaginationContainer').innerHTML = '';
            return;
        }

        // Show loading state
        document.getElementById('boardGameNoResults').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';

        fetch(`/shopify/update-catalog/api/board-games/search?query=${encodeURIComponent(query)}&page=${page}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                displayBoardGameResults(data);
                createBoardGamePagination(data.current_page, data.total_pages, query);
            })
            .catch(error => {
                console.error('Error searching board games:', error);
                document.getElementById('boardGameNoResults').innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Error searching board games. Please try again.';
                document.getElementById('boardGameResultsContainer').style.display = 'none';
                document.getElementById('boardGameResultsCount').style.display = 'none';
                document.getElementById('selectAllBoardGamesBtn').style.display = 'none';
                document.getElementById('pushBoardGamesToShopifyBtn').style.display = 'none';
            });
    }

    function displayBoardGameResults(data) {
        const tableBody = document.getElementById('boardGameResultsTableBody');
        const resultsContainer = document.getElementById('boardGameResultsContainer');
        const noResults = document.getElementById('boardGameNoResults');
        const resultsCount = document.getElementById('boardGameResultsCount');
        const selectAllBtn = document.getElementById('selectAllBoardGamesBtn');

        if (data.games && data.games.length > 0) {
            tableBody.innerHTML = '';

            data.games.forEach(game => {
                const row = document.createElement('tr');

                // Format players
                let playersText = '';
                if (game.min_players && game.max_players) {
                    if (game.min_players === game.max_players) {
                        playersText = game.min_players;
                    } else {
                        playersText = `${game.min_players}-${game.max_players}`;
                    }
                } else if (game.min_players) {
                    playersText = `${game.min_players}+`;
                }

                // Format playing time
                let timeText = game.playing_time ? `${game.playing_time} min` : 'N/A';

                // Determine the best image URL to use (backend provides best_image_url or fallback to manual logic)
                // Use a data URL as fallback to avoid 404 errors
                const fallbackImage = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjNGE1NTY4Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0MFY0MEgyMFYyMFoiIGZpbGw9IiNhMGFlYzAiLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzJkMzc0OCIvPgo8L3N2Zz4K';
                const imageUrl = game.best_image_url || game.thumbnail_url || game.image_url || fallbackImage;

                row.innerHTML = `
                    <td class="border-secondary">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input board-game-checkbox" value="${game._id.$oid || game._id}">
                        </div>
                    </td>
                    <td class="border-secondary">
                        <div class="board-game-image-container" style="width: 60px; height: 60px; position: relative; border-radius: 4px; overflow: hidden; background-color: #2d3748;">
                            <img class="board-game-image lazy-load"
                                 data-src="${imageUrl}"
                                 alt="${game.name}"
                                 style="width: 100%; height: 100%; object-fit: cover; opacity: 0; transition: opacity 0.3s ease;"
                                 loading="lazy">
                            <div class="image-placeholder" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: #4a5568; color: #a0aec0; font-size: 12px;">
                                <i class="fas fa-image"></i>
                            </div>
                        </div>
                    </td>
                    <td class="border-secondary">
                        <strong>${game.name}</strong>
                        ${game.year_published ? `<br><small class="text-muted">(${game.year_published})</small>` : ''}
                    </td>
                    <td class="border-secondary">${game.id || 'N/A'}</td>
                    <td class="border-secondary">${game.year_published || 'N/A'}</td>
                    <td class="border-secondary">${playersText || 'N/A'}</td>
                    <td class="border-secondary">${timeText}</td>
                `;

                tableBody.appendChild(row);
            });

            resultsContainer.style.display = 'block';
            noResults.style.display = 'none';
            resultsCount.textContent = `${data.total_count} results`;
            resultsCount.style.display = 'inline';
            selectAllBtn.style.display = 'inline-block';
            document.getElementById('pushBoardGamesToShopifyBtn').style.display = 'inline-block';

            // Initialize lazy loading for the new images
            initializeLazyLoading();
        } else {
            resultsContainer.style.display = 'none';
            noResults.innerHTML = '<i class="fas fa-info-circle me-2"></i>No board games found matching your search.';
            resultsCount.style.display = 'none';
            selectAllBtn.style.display = 'none';
            document.getElementById('pushBoardGamesToShopifyBtn').style.display = 'none';
        }
    }

    function createBoardGamePagination(currentPage, totalPages, query) {
        const container = document.getElementById('boardGamePaginationContainer');
        container.innerHTML = '';

        if (totalPages <= 1) return;

        const ul = document.createElement('ul');
        ul.className = 'pagination';

        // Previous button
        const prevLi = document.createElement('li');
        prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
        prevLi.innerHTML = `
            <button class="page-link bg-dark text-light border-secondary" ${currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i>
            </button>
        `;
        if (currentPage !== 1) {
            prevLi.querySelector('button').addEventListener('click', () => searchBoardGames(query, currentPage - 1));
        }
        ul.appendChild(prevLi);

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<button class="page-link bg-dark text-light border-secondary">${i}</button>`;
            if (i !== currentPage) {
                li.querySelector('button').addEventListener('click', () => searchBoardGames(query, i));
            }
            ul.appendChild(li);
        }

        // Next button
        const nextLi = document.createElement('li');
        nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
        nextLi.innerHTML = `
            <button class="page-link bg-dark text-light border-secondary" ${currentPage === totalPages ? 'disabled' : ''}>
                <i class="fas fa-chevron-right"></i>
            </button>
        `;
        if (currentPage !== totalPages) {
            nextLi.querySelector('button').addEventListener('click', () => searchBoardGames(query, currentPage + 1));
        }
        ul.appendChild(nextLi);

        container.appendChild(ul);
    }

    // Lazy Loading Implementation for Board Game and Video Game Images
    function initializeLazyLoading() {
        const lazyImages = document.querySelectorAll('.board-game-image.lazy-load:not([data-loaded]), .video-game-image.lazy-load:not([data-loaded])');

        // Fallback SVG for failed images
        const fallbackSvg = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjNGE1NTY4Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0MFY0MEgyMFYyMFoiIGZpbGw9IiNhMGFlYzAiLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzJkMzc0OCIvPgo8L3N2Zz4K';

        // Use Intersection Observer for better performance
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const placeholder = img.nextElementSibling;

                        // Mark as being loaded to prevent re-processing
                        img.setAttribute('data-loaded', 'true');

                        // Set up error handler first to prevent loops
                        img.onerror = function() {
                            // Only set fallback if not already set
                            if (img.src !== fallbackSvg) {
                                img.src = fallbackSvg;
                                img.style.opacity = '1';
                                if (placeholder && placeholder.classList.contains('image-placeholder')) {
                                    placeholder.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                                    placeholder.style.color = '#e53e3e';
                                }
                            }
                        };

                        img.onload = function() {
                            // Fade in the image and hide placeholder
                            img.style.opacity = '1';
                            if (placeholder && placeholder.classList.contains('image-placeholder')) {
                                placeholder.style.display = 'none';
                            }
                        };

                        // Load the image (check if it's already the fallback)
                        const srcToLoad = img.dataset.src;
                        if (srcToLoad && srcToLoad !== fallbackSvg) {
                            img.src = srcToLoad;
                        } else {
                            // Use fallback directly if no valid src
                            img.src = fallbackSvg;
                            img.style.opacity = '1';
                            if (placeholder && placeholder.classList.contains('image-placeholder')) {
                                placeholder.innerHTML = '<i class="fas fa-image"></i>';
                            }
                        }

                        // Stop observing this image
                        observer.unobserve(img);
                    }
                });
            }, {
                // Load images when they're 50px away from viewport
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            lazyImages.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for browsers without Intersection Observer
            lazyImages.forEach(img => {
                const placeholder = img.nextElementSibling;

                // Mark as being loaded
                img.setAttribute('data-loaded', 'true');

                img.onerror = function() {
                    if (img.src !== fallbackSvg) {
                        img.src = fallbackSvg;
                        img.style.opacity = '1';
                        if (placeholder && placeholder.classList.contains('image-placeholder')) {
                            placeholder.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                            placeholder.style.color = '#e53e3e';
                        }
                    }
                };

                img.onload = function() {
                    img.style.opacity = '1';
                    if (placeholder && placeholder.classList.contains('image-placeholder')) {
                        placeholder.style.display = 'none';
                    }
                };

                const srcToLoad = img.dataset.src;
                if (srcToLoad && srcToLoad !== fallbackSvg) {
                    img.src = srcToLoad;
                } else {
                    img.src = fallbackSvg;
                    img.style.opacity = '1';
                    if (placeholder && placeholder.classList.contains('image-placeholder')) {
                        placeholder.innerHTML = '<i class="fas fa-image"></i>';
                    }
                }
            });
        }
    }

    // Board Games Event Listeners
    document.getElementById('boardGameSearchBtn')?.addEventListener('click', function() {
        const query = document.getElementById('boardGameSearchInput').value.trim();
        boardGameSearchQuery = query;
        currentBoardGamePage = 1;
        searchBoardGames(query, 1);
    });

    document.getElementById('boardGameSearchInput')?.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            document.getElementById('boardGameSearchBtn').click();
        }
    });

    // Select All Board Games Button functionality
    document.getElementById('selectAllBoardGamesBtn')?.addEventListener('click', function() {
        const checkboxes = document.querySelectorAll('.board-game-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text
        this.innerHTML = allChecked ?
            '<i class="fas fa-check-square me-2"></i>Select All' :
            '<i class="fas fa-square me-2"></i>Deselect All';
    });

    // Global variables for board game processing
    let selectedBoardGames = [];
    let currentGameIndex = 0;
    let gameDataForShopify = [];
    let boardGameModal = null;
    let isProcessingBoardGames = false; // Flag to prevent duplicate processing

    // Push Board Games to Shopify functionality
    document.getElementById('pushBoardGamesToShopifyBtn')?.addEventListener('click', function() {
        // Prevent duplicate processing
        if (isProcessingBoardGames) {
            showWarningToast('Board games are already being processed. Please wait.');
            return;
        }

        const selectedCheckboxes = document.querySelectorAll('.board-game-checkbox:checked');

        if (selectedCheckboxes.length === 0) {
            showWarningToast('Please select at least one board game to push to Shopify.');
            return;
        }

        // Prepare selected games data
        selectedBoardGames = Array.from(selectedCheckboxes).map(checkbox => {
            const gameRow = checkbox.closest('tr');
            return {
                id: checkbox.value,
                checkbox: checkbox,
                gameRow: gameRow,
                name: getGameName(gameRow),
                year: getGameYear(gameRow),
                image: getGameImage(gameRow)
            };
        });

        // Set processing flag
        isProcessingBoardGames = true;

        // Reset processing variables
        currentGameIndex = 0;
        gameDataForShopify = [];

        // Initialize modal
        boardGameModal = new bootstrap.Modal(document.getElementById('boardGamePriceModal'));

        // Start processing first game
        showGamePriceModal();
    });

    // Helper functions to safely extract game data from table rows
    function getGameName(gameRow) {
        if (!gameRow) return 'Unknown Game';
        // The game name is in the third column (index 2) in a <strong> tag
        const titleCell = gameRow.cells[2];
        if (!titleCell) return 'Unknown Game';

        const strongElement = titleCell.querySelector('strong');
        return strongElement ? strongElement.textContent.trim() : 'Unknown Game';
    }

    function getGameYear(gameRow) {
        if (!gameRow) return '';
        // The year is in the third column (index 2) in a <small> tag with text-muted class
        const titleCell = gameRow.cells[2];
        if (!titleCell) return '';

        const yearElement = titleCell.querySelector('small.text-muted');
        if (yearElement) {
            // Extract year from text like "(2018)"
            const yearText = yearElement.textContent.trim();
            const yearMatch = yearText.match(/\((\d{4})\)/);
            return yearMatch ? yearMatch[1] : yearText;
        }
        return '';
    }

    function getGameImage(gameRow) {
        if (!gameRow) return '';
        // The image is in the second column (index 1)
        const imageCell = gameRow.cells[1];
        if (!imageCell) return '';

        const imageElement = imageCell.querySelector('.board-game-image') ||
                           imageCell.querySelector('img');
        if (imageElement) {
            // Return the data-src if available (for lazy loading), otherwise src
            return imageElement.dataset.src || imageElement.src || '';
        }
        return '';
    }



    // Function to show modal for current game
    function showGamePriceModal() {
        if (currentGameIndex >= selectedBoardGames.length) {
            // All games processed, push to Shopify
            pushAllGamesToShopify();
            return;
        }

        const currentGame = selectedBoardGames[currentGameIndex];

        // Update modal content
        document.getElementById('currentGameName').textContent = currentGame.name;
        document.getElementById('currentGameYear').textContent = currentGame.year;
        document.getElementById('currentGameImage').src = currentGame.image || '';
        document.getElementById('currentGameProgress').textContent = `Game ${currentGameIndex + 1} of ${selectedBoardGames.length}`;

        // Update modal info text
        if (selectedBoardGames.length === 1) {
            document.getElementById('modalGameInfo').textContent = 'Set the price and quantity for this board game before pushing to Shopify.';
        } else {
            document.getElementById('modalGameInfo').textContent = `Set the price and quantity for each board game. Currently setting: ${currentGame.name}`;
        }

        // Update button text
        if (currentGameIndex === selectedBoardGames.length - 1) {
            document.getElementById('pushButtonText').textContent = 'Push All to Shopify';
        } else {
            document.getElementById('pushButtonText').textContent = 'Next Game';
        }

        // Show/hide skip button for multiple games
        const skipButton = document.getElementById('skipCurrentGame');
        if (selectedBoardGames.length > 1) {
            skipButton.style.display = 'inline-block';
        } else {
            skipButton.style.display = 'none';
        }

        // Reset form values
        document.getElementById('boardGamePrice').value = '';
        document.getElementById('boardGameQuantity').value = '1';

        // Show modal
        boardGameModal.show();
    }

    // Handle the actual push to Shopify from the modal (ensure single event listener)
    const confirmPushBtn = document.getElementById('confirmPushToShopify');
    if (confirmPushBtn && !confirmPushBtn.hasAttribute('data-listener-added')) {
        confirmPushBtn.setAttribute('data-listener-added', 'true');
        confirmPushBtn.addEventListener('click', function() {
        const price = document.getElementById('boardGamePrice').value;
        const quantity = document.getElementById('boardGameQuantity').value;

        // Validate inputs
        if (!price || parseFloat(price) < 0) {
            showWarningToast('Please enter a valid price.');
            return;
        }

        if (!quantity || parseInt(quantity) < 0) {
            showWarningToast('Please enter a valid quantity.');
            return;
        }

        // Store current game data
        const currentGame = selectedBoardGames[currentGameIndex];
        gameDataForShopify.push({
            id: currentGame.id,
            name: currentGame.name,
            price: parseFloat(price),
            quantity: parseInt(quantity)
        });

        // Move to next game
        currentGameIndex++;

        // Hide modal temporarily
        boardGameModal.hide();

        // Show next game or push all to Shopify
        setTimeout(() => {
            showGamePriceModal();
        }, 300);
        });
    }

    // Handle skip current game
    document.getElementById('skipCurrentGame')?.addEventListener('click', function() {
        // Move to next game without storing data
        currentGameIndex++;

        // Hide modal temporarily
        boardGameModal.hide();

        // Show next game or push all to Shopify
        setTimeout(() => {
            showGamePriceModal();
        }, 300);
    });

    // Function to push all games to Shopify
    function pushAllGamesToShopify() {
        if (gameDataForShopify.length === 0) {
            showWarningToast('No games to push to Shopify.');
            isProcessingBoardGames = false; // Reset flag
            return;
        }

        // Show loading overlay
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingText = document.getElementById('loadingOverlayText');
        loadingText.textContent = `Pushing ${gameDataForShopify.length} board games to Shopify...`;
        loadingOverlay.classList.remove('d-none');

        // Process each game individually
        let successCount = 0;
        let failCount = 0;
        let results = [];

        async function processGame(gameData, index) {
            try {
                const response = await fetch('/shopify/update-catalog/api/board-games/push-to-shopify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        gameIds: [gameData.id],
                        price: gameData.price,
                        quantity: gameData.quantity
                    })
                });

                const data = await response.json();

                if (data.error) {
                    failCount++;
                    results.push({
                        name: gameData.name,
                        success: false,
                        error: data.error
                    });
                } else if (data.type === 'success') {
                    successCount++;
                    results.push({
                        name: gameData.name,
                        success: true,
                        price: gameData.price,
                        quantity: gameData.quantity
                    });
                } else {
                    failCount++;
                    results.push({
                        name: gameData.name,
                        success: false,
                        error: data.message || 'Unknown error'
                    });
                }

                // Update loading text
                loadingText.textContent = `Processing game ${index + 1} of ${gameDataForShopify.length}: ${gameData.name}`;

            } catch (error) {
                failCount++;
                results.push({
                    name: gameData.name,
                    success: false,
                    error: error.message
                });
            }
        }

        // Process all games sequentially
        (async () => {
            for (let i = 0; i < gameDataForShopify.length; i++) {
                await processGame(gameDataForShopify[i], i);
            }

            // Hide loading overlay
            loadingOverlay.classList.add('d-none');

            // Show results
            let message = '';
            if (successCount > 0 && failCount === 0) {
                message = `Success! Pushed ${successCount} board games to Shopify!`;
            } else if (successCount > 0 && failCount > 0) {
                message = `Partial Success: ${successCount} games pushed successfully, ${failCount} failed.\n\nCheck the console for details.`;
                console.log('Push results:', results);
            } else {
                message = `Failed to push all ${gameDataForShopify.length} board games to Shopify.`;
                console.log('Push results:', results);
            }

            if (results.type === 'success') {
                const actions = [{
                    text: 'View in Shopify',
                    onclick: 'window.open("https://admin.shopify.com", "_blank")'
                }];
                showSuccessToast(message, 8000, actions);
            } else {
                showErrorToast(message);
            }

            // Reset variables and processing flag
            selectedBoardGames = [];
            currentGameIndex = 0;
            gameDataForShopify = [];
            isProcessingBoardGames = false;
        })();
    }

    // Select All Board Games functionality
    document.getElementById('selectAllBoardGamesCheckbox')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.board-game-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });

    // Update select all checkbox when individual checkboxes change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('board-game-checkbox')) {
            const allCheckboxes = document.querySelectorAll('.board-game-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.board-game-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAllBoardGamesCheckbox');

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
        }
    });

    // Board Games Push Button
    document.getElementById('pushToBoardGamesBtn')?.addEventListener('click', function() {
        document.getElementById('pushToUpdatesBtn').click();
    });

    // Function to hide/show sections based on active tab
    function toggleSectionsForTab(activeTabId) {
        const settingsAccordion = document.getElementById('settingsAccordion');
        const gameSelect = document.getElementById('gameSelect');
        const checkInventoryBtn = document.getElementById('checkInventoryBtn');

        // Find parent cards by traversing up from the elements
        const filtersCard = gameSelect ? gameSelect.closest('.card.bg-dark.border-secondary.mb-4') : null;
        const inventoryPriorityCard = checkInventoryBtn ? checkInventoryBtn.closest('.card.bg-dark.border-secondary.mb-4') : null;

        if (activeTabId === 'boardgames-content' || activeTabId === 'videogames-content') {
            // Hide sections for board games and video games tabs
            if (settingsAccordion) settingsAccordion.style.display = 'none';
            if (filtersCard) filtersCard.style.display = 'none';
            if (inventoryPriorityCard) inventoryPriorityCard.style.display = 'none';
        } else {
            // Show sections for other tabs
            if (settingsAccordion) settingsAccordion.style.display = 'block';
            if (filtersCard) filtersCard.style.display = 'block';
            if (inventoryPriorityCard) inventoryPriorityCard.style.display = 'block';
        }
    }

    // Add event listeners to all tab buttons
    document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tabButton => {
        tabButton.addEventListener('shown.bs.tab', function(e) {
            const targetId = e.target.getAttribute('data-bs-target').substring(1); // Remove the # from target
            toggleSectionsForTab(targetId);
        });
    });

    // Initialize on page load - check which tab is active
    document.addEventListener('DOMContentLoaded', function() {
        const activeTab = document.querySelector('.tab-pane.active');
        if (activeTab) {
            toggleSectionsForTab(activeTab.id);
        }
    });

    // Video Games Search functionality
    let userCurrency = 'USD'; // Default currency

    // Fetch user currency on page load
    async function fetchUserCurrency() {
        try {
            const response = await fetch('/shopify/update-catalog/api/user-currency');
            if (response.ok) {
                const data = await response.json();
                userCurrency = data.currency;
                console.log('User currency:', userCurrency);

                // Update currency notice in video games table
                const currencyCodeSpan = document.getElementById('videoGamesCurrencyCode');
                if (currencyCodeSpan) {
                    currencyCodeSpan.textContent = userCurrency;
                }
            }
        } catch (error) {
            console.error('Error fetching user currency:', error);
        }
    }

    // Initialize user currency
    fetchUserCurrency();

    // Video Games Search functionality - now handled by the new filter system
    // Event listeners are set in the HTML onclick and onkeypress attributes

    // Old search function removed - replaced with performVideoGameFilteredSearch()

    // Get variant prices for table columns (prices are already converted by backend)
    function getVideoGameVariantPrices(game) {
        return {
            new: game['new-price'] && game['new-price'] !== '$0.00' ? game['new-price'] : null,
            cib: game['cib-price'] && game['cib-price'] !== '$0.00' ? game['cib-price'] : null,
            loose: game['loose-price'] && game['loose-price'] !== '$0.00' ? game['loose-price'] : null,
            boxOnly: game['box-only-price'] && game['box-only-price'] !== '$0.00' ? game['box-only-price'] : null,
            manualOnly: game['manual-only-price'] && game['manual-only-price'] !== '$0.00' ? game['manual-only-price'] : null
        };
    }

    // Calculate price information for video games table display (kept for compatibility)
    function calculateVideoGamePriceInfo(game) {
        const priceFields = [
            'graded-price', 'new-price', 'cib-price', 'bgs-10-price',
            'loose-price', 'box-only-price', 'manual-only-price'
        ];

        let highestPrice = 0;
        let highestPriceStr = 'N/A';
        let variantCount = 0;

        priceFields.forEach(field => {
            const priceStr = game[field];
            if (priceStr && priceStr !== 'N/A' && priceStr !== '$0.00') {
                variantCount++;

                // Extract numeric value from price string (remove currency symbols and commas)
                const numericValue = parseFloat(priceStr.replace(/[^0-9.]/g, ''));

                if (!isNaN(numericValue) && numericValue > highestPrice) {
                    highestPrice = numericValue;
                    highestPriceStr = priceStr;
                }
            }
        });

        return {
            highestPrice: highestPriceStr,
            variantCount: variantCount
        };
    }

    function displayVideoGameResults(games, totalCount) {
        const resultsContainer = document.getElementById('videoGameResultsContainer');
        const searchPrompt = document.getElementById('videoGameSearchPrompt');
        const noResults = document.getElementById('videoGameNoResults');
        const resultsCount = document.getElementById('videoGameResultsCount');
        const tableBody = document.getElementById('videoGameResultsTableBody');

        if (games && games.length > 0) {
            // Hide prompt and no results, show results
            searchPrompt.style.display = 'none';
            noResults.style.display = 'none';
            resultsContainer.style.display = 'block';

            // Update count
            resultsCount.textContent = `${games.length} results`;

            // Clear previous results
            tableBody.innerHTML = '';

            // Add each game to the table with variant prices as columns
            games.forEach(game => {
                const row = document.createElement('tr');
                row.style.cursor = 'pointer';
                row.classList.add('video-game-row');
                row.setAttribute('data-game-id', game._id.$oid || game._id);

                const imageUrl = game.image_url || '/static/images/no-image.png';

                // Get variant prices for columns
                const variantPrices = getVideoGameVariantPrices(game);

                row.innerHTML = `
                    <td class="border-secondary">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input video-game-checkbox" value="${game._id.$oid || game._id}">
                        </div>
                    </td>
                    <td class="border-secondary">
                        <div class="video-game-image-container" style="width: 60px; height: 60px; position: relative; border-radius: 4px; overflow: hidden; background-color: #2d3748;">
                            <img class="video-game-image"
                                 src="${imageUrl}"
                                 alt="${game.name || 'Video Game'}"
                                 style="width: 100%; height: 100%; object-fit: cover;"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjNGE1NTY4Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0MFY0MEgyMFYyMFoiIGZpbGw9IiNhMGFlYzAiLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzJkMzc0OCIvPgo8L3N2Zz4K'">
                        </div>
                    </td>
                    <td class="border-secondary">
                        <strong class="video-game-title">${game.name || 'Unknown Game'}</strong>
                        ${game.release_year ? `<br><small class="text-muted video-game-year">(${game.release_year})</small>` : ''}
                    </td>
                    <td class="border-secondary">
                        <span class="badge bg-info">${game.platform || 'Unknown'}</span>
                    </td>
                    <td class="border-secondary">
                        <span class="badge bg-secondary">${game.genre || 'Unknown'}</span>
                    </td>
                    <td class="border-secondary">${game.release_year || 'N/A'}</td>
                    <td class="border-secondary text-center">
                        ${variantPrices.new ? `<span class="badge bg-success">${variantPrices.new}</span>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td class="border-secondary text-center">
                        ${variantPrices.cib ? `<span class="badge bg-success">${variantPrices.cib}</span>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td class="border-secondary text-center">
                        ${variantPrices.loose ? `<span class="badge bg-success">${variantPrices.loose}</span>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td class="border-secondary text-center">
                        ${variantPrices.boxOnly ? `<span class="badge bg-success">${variantPrices.boxOnly}</span>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td class="border-secondary text-center">
                        ${variantPrices.manualOnly ? `<span class="badge bg-success">${variantPrices.manualOnly}</span>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td class="border-secondary">
                        <small class="text-muted">${game.upc_display || 'N/A'}</small>
                    </td>
                `;

                // Add click event to show details modal (but not on checkbox)
                row.addEventListener('click', function(e) {
                    if (e.target.type !== 'checkbox') {
                        showVideoGameDetails(game);
                    }
                });

                tableBody.appendChild(row);
            });

        } else {
            showVideoGameNoResults();
        }
    }

    function showVideoGameNoResults() {
        const resultsContainer = document.getElementById('videoGameResultsContainer');
        const searchPrompt = document.getElementById('videoGameSearchPrompt');
        const noResults = document.getElementById('videoGameNoResults');
        const resultsCount = document.getElementById('videoGameResultsCount');

        searchPrompt.style.display = 'none';
        resultsContainer.style.display = 'none';
        noResults.style.display = 'block';
        resultsCount.textContent = '0 results';
    }

    // Select All Video Games functionality
    document.getElementById('selectAllVideoGamesCheckbox')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.video-game-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateVideoGamePushButton();
    });

    // Update select all checkbox when individual checkboxes change
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('video-game-checkbox')) {
            const allCheckboxes = document.querySelectorAll('.video-game-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.video-game-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAllVideoGamesCheckbox');

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }

            updateVideoGamePushButton();
        }
    });

    function updateVideoGamePushButton() {
        const checkedCheckboxes = document.querySelectorAll('.video-game-checkbox:checked');
        const pushButton = document.getElementById('pushVideoGamesToShopifyBtn');

        if (checkedCheckboxes.length > 0) {
            pushButton.style.display = 'inline-block';
        } else {
            pushButton.style.display = 'none';
        }
    }

    // Video Games Push to Shopify functionality
    document.getElementById('pushVideoGamesToShopifyBtn')?.addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.video-game-checkbox:checked');

        if (selectedCheckboxes.length === 0) {
            showWarningToast('Please select at least one video game to push to Shopify.');
            return;
        }

        // Prepare selected games data
        const selectedVideoGames = Array.from(selectedCheckboxes).map(checkbox => {
            const gameRow = checkbox.closest('tr');
            return {
                id: checkbox.value,
                checkbox: checkbox,
                gameRow: gameRow,
                name: getVideoGameName(gameRow),
                year: getVideoGameYear(gameRow),
                image: getVideoGameImage(gameRow),
                platform: getVideoGamePlatform(gameRow),
                genre: getVideoGameGenre(gameRow)
            };
        });

        // Show professional confirmation modal
        showVideoGameConfirmationModal(selectedVideoGames);
    });

    // Function to show professional video game confirmation modal
    function showVideoGameConfirmationModal(selectedVideoGames) {
        const gameNames = selectedVideoGames.map(game => game.name).join(', ');

        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="videoGameConfirmModal" tabindex="-1" aria-labelledby="videoGameConfirmModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content bg-dark text-light">
                        <div class="modal-header border-secondary">
                            <h5 class="modal-title" id="videoGameConfirmModalLabel">
                                <i class="fas fa-upload me-2"></i>Confirm Shopify Push
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info border-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Push Confirmation</strong>
                            </div>

                            <p><strong>Selected Games (${selectedVideoGames.length}):</strong></p>
                            <div class="bg-secondary p-3 rounded mb-3">
                                <small class="text-light">${gameNames}</small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-primary text-white mb-3">
                                        <div class="card-body text-center">
                                            <i class="fas fa-coins fa-2x mb-2"></i>
                                            <h6>Currency</h6>
                                            <strong>${userCurrency}</strong>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-warning text-dark mb-3">
                                        <div class="card-body text-center">
                                            <i class="fas fa-boxes fa-2x mb-2"></i>
                                            <h6>Quantity</h6>
                                            <strong>0 (Inventory Tracking)</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-warning border-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Note:</strong> Products will be created as <strong>active</strong> with <strong>0 inventory</strong>. You can update quantities in Shopify after creation.
                            </div>
                        </div>
                        <div class="modal-footer border-secondary">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-primary" id="confirmVideoGamePushModal">
                                <i class="fas fa-upload me-2"></i>Push to Shopify
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('videoGameConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('videoGameConfirmModal'));
        modal.show();

        // Handle confirm button
        document.getElementById('confirmVideoGamePushModal').addEventListener('click', function() {
            // Close modal
            modal.hide();

            // Push games with quantity 0
            pushVideoGamesToShopify(selectedVideoGames, 0);
        });

        // Clean up modal when hidden
        document.getElementById('videoGameConfirmModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // Function to push video games to Shopify
    async function pushVideoGamesToShopify(selectedVideoGames, quantity = 1) {
        const pushBtn = document.getElementById('pushVideoGamesToShopifyBtn');
        const originalText = pushBtn.innerHTML;

        try {
            // Update button state
            pushBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Pushing to Shopify...';
            pushBtn.disabled = true;

            // Show loading toast
            const loadingToastId = showLoadingToast(`Pushing ${selectedVideoGames.length} video game(s) to Shopify...`);

            // Extract game IDs
            const gameIds = selectedVideoGames.map(game => game.id);

            // Make API call
            const response = await fetch('/shopify/update-catalog/api/video-games/push-to-shopify', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    gameIds: gameIds,
                    quantity: quantity
                })
            });

            const data = await response.json();
            hideToast(loadingToastId);

            if (data.error) {
                showErrorToast(`Error pushing video games to Shopify: ${data.error}`);
            } else if (data.type === 'success') {
                const message = `${data.message}<br><br><strong>Currency:</strong> ${data.currency}<br><strong>Successful:</strong> ${data.successful_pushes.length}<br><strong>Failed:</strong> ${data.failed_pushes.length}`;

                const actions = [];
                if (data.successful_pushes.length > 0) {
                    actions.push({
                        text: 'View in Shopify',
                        onclick: 'window.open("https://admin.shopify.com", "_blank")'
                    });
                }

                showSuccessToast(message, 8000, actions);

                // Uncheck selected games
                selectedVideoGames.forEach(game => {
                    if (game.checkbox) {
                        game.checkbox.checked = false;
                    }
                });

                // Update button visibility
                updateVideoGamePushButtonVisibility();
            } else {
                showErrorToast(`Error: ${data.message || 'Unknown error occurred'}`);
            }

        } catch (error) {
            console.error('Error pushing video games to Shopify:', error);
            hideToast(loadingToastId);
            showErrorToast('Error pushing video games to Shopify. Please try again.');
        } finally {
            // Restore button state
            pushBtn.innerHTML = originalText;
            pushBtn.disabled = false;
        }
    }

    // Helper functions to safely extract video game data from table rows
    function getVideoGameName(gameRow) {
        if (!gameRow) return 'Unknown Game';
        const titleElement = gameRow.querySelector('.video-game-title');
        return titleElement ? titleElement.textContent.trim() : 'Unknown Game';
    }

    function getVideoGameYear(gameRow) {
        if (!gameRow) return '';
        const yearElement = gameRow.querySelector('.video-game-year');
        if (yearElement) {
            const yearText = yearElement.textContent.trim();
            const yearMatch = yearText.match(/\((\d{4})\)/);
            return yearMatch ? yearMatch[1] : yearText;
        }
        return '';
    }

    function getVideoGameImage(gameRow) {
        if (!gameRow) return '';
        const imageElement = gameRow.querySelector('.video-game-image');
        if (imageElement) {
            return imageElement.src || '';
        }
        return '';
    }

    function getVideoGamePlatform(gameRow) {
        if (!gameRow) return '';
        const platformElement = gameRow.cells[3]?.querySelector('.badge');
        return platformElement ? platformElement.textContent.trim() : '';
    }

    function getVideoGameGenre(gameRow) {
        if (!gameRow) return '';
        const genreElement = gameRow.cells[4]?.querySelector('.badge');
        return genreElement ? genreElement.textContent.trim() : '';
    }

    // Show Video Game Details Modal with Variants
    function showVideoGameDetails(game) {
        console.log('Showing video game details for:', game);

        // Populate basic game info
        document.getElementById('modalGameTitle').textContent = game.name || 'Unknown Game';
        document.getElementById('modalGamePlatform').textContent = game.platform || 'Unknown';
        document.getElementById('modalGameGenre').textContent = game.genre || 'Unknown';
        document.getElementById('modalGameDeveloper').textContent = game.developer || 'Unknown';
        document.getElementById('modalGamePublisher').textContent = game.publisher || 'Unknown';
        document.getElementById('modalGamePlayers').textContent = game['player-count'] || 'Unknown';
        document.getElementById('modalGameDiscCount').textContent = game['disc-count'] || 'Unknown';
        document.getElementById('modalGameUPC').textContent = game.upc_display || 'N/A';
        document.getElementById('modalGameDescription').textContent = game.description || 'No description available.';

        // Set game image
        const modalImage = document.getElementById('modalGameImage');
        modalImage.src = game.image_url || '';
        modalImage.alt = game.name || 'Video Game';

        // Format release date
        let releaseDate = 'Unknown';
        if (game['release-date']) {
            try {
                const date = new Date(game['release-date'].$date || game['release-date']);
                releaseDate = date.getFullYear();
            } catch (e) {
                console.warn('Error parsing release date:', e);
            }
        }
        document.getElementById('modalGameReleaseDate').textContent = releaseDate;

        // Fetch and display user currency info
        fetchUserCurrencyInfo();

        // Create price variants (ordered by highest price first)
        const variants = createVideoGameVariants(game);
        populateVideoGameVariants(variants);

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('videoGameDetailsModal'));
        modal.show();
    }

    // Fetch user currency information
    function fetchUserCurrencyInfo() {
        fetch('/shopify/update-catalog/api/user-currency')
            .then(response => response.json())
            .then(data => {
                const currency = data.currency || 'USD';
                userCurrency = currency; // Update global currency variable
                const currencyInfo = document.getElementById('modalCurrencyInfo');
                if (currency !== 'USD') {
                    currencyInfo.textContent = `(Converted to ${currency})`;
                } else {
                    currencyInfo.textContent = '';
                }
                console.log('Modal currency info updated:', currency);
            })
            .catch(error => {
                console.warn('Error fetching user currency:', error);
                document.getElementById('modalCurrencyInfo').textContent = '';
            });
    }

    // Create price variants from game data
    function createVideoGameVariants(game) {
        const variants = [];

        // Define all possible price types with their display names
        const priceTypes = [
            { key: 'graded-price', name: 'Graded' },
            { key: 'new-price', name: 'New/Sealed' },
            { key: 'cib-price', name: 'Complete in Box (CIB)' },
            { key: 'bgs-10-price', name: 'BGS 10 Graded' },
            { key: 'loose-price', name: 'Loose/Cart Only' },
            { key: 'box-only-price', name: 'Box Only' },
            { key: 'manual-only-price', name: 'Manual Only' }
        ];

        // Extract variants that have prices
        priceTypes.forEach(priceType => {
            const price = game[priceType.key];
            if (price && price !== '$0.00' && price !== '0' && price !== '') {
                const variant = {
                    condition: priceType.name,
                    price: price,
                    priceValue: parseFloat(price.replace(/[$,]/g, '')) || 0
                };
                variants.push(variant);
            }
        });

        // Sort by price (highest first)
        variants.sort((a, b) => b.priceValue - a.priceValue);

        return variants;
    }

    // Populate the variants table in the modal
    function populateVideoGameVariants(variants) {
        const variantsTableBody = document.getElementById('modalGameVariants');
        variantsTableBody.innerHTML = '';

        if (variants.length === 0) {
            variantsTableBody.innerHTML = `
                <tr>
                    <td colspan="3" class="text-center text-muted">No price variants available</td>
                </tr>
            `;
            return;
        }

        variants.forEach((variant, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input variant-checkbox"
                               id="variant-${index}" value="${index}">
                    </div>
                </td>
                <td>
                    <span class="badge bg-primary">${variant.condition}</span>
                </td>
                <td>
                    <strong class="text-success">${variant.price}</strong>
                </td>
            `;
            variantsTableBody.appendChild(row);
        });

        // Add event listeners for variant selection
        setupVariantSelection();
    }

    // Setup variant selection functionality
    function setupVariantSelection() {
        // Select all variants checkbox
        const selectAllVariants = document.getElementById('selectAllVariants');
        selectAllVariants.addEventListener('change', function() {
            const variantCheckboxes = document.querySelectorAll('.variant-checkbox');
            variantCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updatePushVariantsButton();
        });

        // Individual variant checkboxes
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('variant-checkbox')) {
                const allVariantCheckboxes = document.querySelectorAll('.variant-checkbox');
                const checkedVariantCheckboxes = document.querySelectorAll('.variant-checkbox:checked');
                const selectAllCheckbox = document.getElementById('selectAllVariants');

                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allVariantCheckboxes.length === checkedVariantCheckboxes.length;
                    selectAllCheckbox.indeterminate = checkedVariantCheckboxes.length > 0 && checkedVariantCheckboxes.length < allVariantCheckboxes.length;
                }

                updatePushVariantsButton();
            }
        });
    }

    // Update the push variants button visibility
    function updatePushVariantsButton() {
        const checkedVariants = document.querySelectorAll('.variant-checkbox:checked');
        const pushButton = document.getElementById('pushSelectedVariantsBtn');

        if (checkedVariants.length > 0) {
            pushButton.style.display = 'inline-block';
        } else {
            pushButton.style.display = 'none';
        }
    }

    document.getElementById('pushToWarhammerBtn')?.addEventListener('click', function() {
        document.getElementById('pushToUpdatesBtn').click();
    });

    document.getElementById('pushToCitadelBtn')?.addEventListener('click', function() {
        document.getElementById('pushToUpdatesBtn').click();
    });

    document.getElementById('pushToCustomBtn')?.addEventListener('click', function() {
        document.getElementById('pushToUpdatesBtn').click();
    });

    document.getElementById('pushToBoardGamesBtn')?.addEventListener('click', function() {
        document.getElementById('pushToUpdatesBtn').click();
    });

    document.getElementById('videogames-tab')?.addEventListener('click', function() {
        console.log('Video Games tab clicked');
    });

    document.getElementById('warhammer-tab')?.addEventListener('click', function() {
        console.log('Warhammer tab clicked');
    });

    document.getElementById('citadel-tab')?.addEventListener('click', function() {
        console.log('Citadel tab clicked');
    });

    document.getElementById('custom-tab')?.addEventListener('click', function() {
        console.log('Custom Items tab clicked');
    });

    // ===== TOAST NOTIFICATION SYSTEM =====

    /**
     * Professional Toast Notification System
     * Provides consistent, modern notifications across the entire application
     */

    let toastCounter = 0;

    function showToast(message, type = 'info', duration = 5000, actions = []) {
        const toastContainer = document.querySelector('.toast-container');
        const toastId = `toast-${++toastCounter}`;

        // Define toast types and their properties
        const toastTypes = {
            'success': {
                bgClass: 'bg-success',
                icon: 'fas fa-check-circle',
                textClass: 'text-white'
            },
            'error': {
                bgClass: 'bg-danger',
                icon: 'fas fa-exclamation-circle',
                textClass: 'text-white'
            },
            'warning': {
                bgClass: 'bg-warning',
                icon: 'fas fa-exclamation-triangle',
                textClass: 'text-dark'
            },
            'info': {
                bgClass: 'bg-info',
                icon: 'fas fa-info-circle',
                textClass: 'text-white'
            },
            'loading': {
                bgClass: 'bg-primary',
                icon: 'fas fa-spinner fa-spin',
                textClass: 'text-white'
            }
        };

        const config = toastTypes[type] || toastTypes['info'];

        // Build action buttons HTML
        let actionsHtml = '';
        if (actions.length > 0) {
            actionsHtml = '<div class="mt-2 pt-2 border-top border-light">';
            actions.forEach(action => {
                actionsHtml += `<button type="button" class="btn btn-sm btn-outline-light me-2" onclick="${action.onclick}">${action.text}</button>`;
            });
            actionsHtml += '</div>';
        }

        // Create toast HTML
        const toastHtml = `
            <div id="${toastId}" class="toast ${config.bgClass} ${config.textClass}" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="${duration > 0}">
                <div class="toast-header ${config.bgClass} ${config.textClass} border-0">
                    <i class="${config.icon} me-2"></i>
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                    ${actionsHtml}
                </div>
            </div>
        `;

        // Add toast to container
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // Initialize and show toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: duration > 0,
            delay: duration
        });

        // Auto-remove toast after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });

        toast.show();

        return toastId;
    }

    // Convenience functions for different toast types
    function showSuccessToast(message, duration = 5000, actions = []) {
        return showToast(message, 'success', duration, actions);
    }

    function showErrorToast(message, duration = 8000, actions = []) {
        return showToast(message, 'error', duration, actions);
    }

    function showWarningToast(message, duration = 6000, actions = []) {
        return showToast(message, 'warning', duration, actions);
    }

    function showInfoToast(message, duration = 5000, actions = []) {
        return showToast(message, 'info', duration, actions);
    }

    function showLoadingToast(message, duration = 0) {
        return showToast(message, 'loading', duration);
    }

    function hideToast(toastId) {
        const toastElement = document.getElementById(toastId);
        if (toastElement) {
            const toast = bootstrap.Toast.getInstance(toastElement);
            if (toast) {
                toast.hide();
            }
        }
    }

    // Enhanced notification functions that replace old alert() calls
    function showNotification(message, type = 'info', options = {}) {
        const {
            duration = type === 'error' ? 8000 : 5000,
            actions = [],
            persistent = false
        } = options;

        return showToast(message, type, persistent ? 0 : duration, actions);
    }

    // ===== END TOAST NOTIFICATION SYSTEM =====

    // ===== VIDEO GAME FILTER SYSTEM =====

    // Load platforms when page loads
    async function loadVideoGamePlatforms() {
        try {
            const response = await fetch('/shopify/update-catalog/api/video-games/platforms');
            if (response.ok) {
                const data = await response.json();
                console.log('Platform API response:', data);
                const platformSelect = document.getElementById('platformFilter');

                // Clear existing options except the first one
                platformSelect.innerHTML = '<option value="">All Platforms</option>';

                // Add platform options
                if (data.platforms && data.platforms.length > 0) {
                    data.platforms.forEach(platform => {
                        const option = document.createElement('option');
                        option.value = platform;
                        option.textContent = platform;
                        platformSelect.appendChild(option);
                        console.log(`Added platform: "${platform}"`);
                    });
                    console.log(`Loaded ${data.platforms.length} video game platforms`);
                } else {
                    console.log('No platforms found in API response');
                }
            } else {
                console.error('Platform API response not OK:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('Error loading video game platforms:', error);
            showErrorToast('Error loading platforms. Please refresh the page.');
        }
    }

    // Load genres based on selected platform
    async function loadVideoGameGenres(platform = '') {
        try {
            const genreSelect = document.getElementById('genreFilter');

            if (!platform) {
                // Reset genres when no platform selected
                genreSelect.innerHTML = '<option value="">All Genres</option>';
                genreSelect.disabled = true;
                return;
            }

            // Show loading state
            genreSelect.innerHTML = '<option value="">Loading genres...</option>';
            genreSelect.disabled = true;

            const response = await fetch(`/shopify/update-catalog/api/video-games/genres?platform=${encodeURIComponent(platform)}`);
            if (response.ok) {
                const data = await response.json();
                console.log('Genre API response:', data);

                // Clear and populate genres
                genreSelect.innerHTML = '<option value="">All Genres</option>';

                if (data.genres && data.genres.length > 0) {
                    data.genres.forEach(genre => {
                        const option = document.createElement('option');
                        option.value = genre;
                        option.textContent = genre;
                        genreSelect.appendChild(option);
                    });
                    console.log(`Loaded ${data.genres.length} genres for platform: ${platform}`);
                } else {
                    console.log(`No genres found for platform: ${platform}`);
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = 'No genres available';
                    genreSelect.appendChild(option);
                }

                genreSelect.disabled = false;
            } else {
                console.error('Genre API response not OK:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('Error loading video game genres:', error);
            const genreSelect = document.getElementById('genreFilter');
            genreSelect.innerHTML = '<option value="">Error loading genres</option>';
            showErrorToast('Error loading genres. Please try again.');
        }
    }

    // Handle platform selection change
    document.getElementById('platformFilter')?.addEventListener('change', function() {
        const selectedPlatform = this.value;
        loadVideoGameGenres(selectedPlatform);

        // Clear previous results when filter changes
        hideVideoGameResults();
    });

    // Handle genre selection change
    document.getElementById('genreFilter')?.addEventListener('change', function() {
        // Clear previous results when filter changes
        hideVideoGameResults();
    });

    // Enhanced video game search function with filters (moved to global scope)
    window.performVideoGameFilteredSearch = function() {
        const query = document.getElementById('videoGameSearchInput').value.trim();
        const platform = document.getElementById('platformFilter').value;
        const genre = document.getElementById('genreFilter').value;

        // Validate that at least one search criteria is provided
        if (!query && !platform && !genre) {
            showWarningToast('Please provide at least one search criteria: title, platform, or genre.');
            return;
        }

        // Show loading state
        showVideoGameLoadingState();
        const searchBtn = document.getElementById('searchVideoGamesBtn');
        const originalText = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Searching...';
        searchBtn.disabled = true;

        // Build search description for toast
        const searchParts = [];
        if (query) searchParts.push(`"${query}"`);
        if (platform) searchParts.push(`Platform: ${platform}`);
        if (genre) searchParts.push(`Genre: ${genre}`);
        const searchDescription = searchParts.join(', ');

        // Show loading toast
        const loadingToastId = showLoadingToast(`Searching for video games: ${searchDescription}...`);

        // Build query parameters
        const params = new URLSearchParams();
        if (query) params.append('query', query);
        if (platform) params.append('platform', platform);
        if (genre) params.append('genre', genre);
        params.append('page', '1');

        // Perform search
        fetch(`/shopify/update-catalog/api/video-games/search?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                displayVideoGameResults(data.games, data.total_count);
                hideToast(loadingToastId);
                showSuccessToast(`Found ${data.total_count} video games matching your criteria`);
            })
            .catch(error => {
                console.error('Error searching video games:', error);
                hideToast(loadingToastId);
                showErrorToast('Error searching video games. Please try again.');
                showVideoGameNoResults();
            })
            .finally(() => {
                // Restore button state
                searchBtn.innerHTML = originalText;
                searchBtn.disabled = false;
                hideVideoGameLoadingState();
            });
    };

    // Show video game loading state (global scope)
    window.showVideoGameLoadingState = function() {
        document.getElementById('videoGameLoadingState').style.display = 'block';
        document.getElementById('videoGameSearchPrompt').style.display = 'none';
        document.getElementById('videoGameResultsContainer').style.display = 'none';
        document.getElementById('videoGameNoResults').style.display = 'none';
    };

    // Hide video game loading state (global scope)
    window.hideVideoGameLoadingState = function() {
        document.getElementById('videoGameLoadingState').style.display = 'none';
    };

    // Hide video game results (global scope)
    window.hideVideoGameResults = function() {
        document.getElementById('videoGameResultsContainer').style.display = 'none';
        document.getElementById('videoGameNoResults').style.display = 'none';
        document.getElementById('videoGameSearchPrompt').style.display = 'block';
    };

    // Load platforms when video games tab is clicked
    document.getElementById('videogames-tab')?.addEventListener('click', function() {
        console.log('Video Games tab clicked');
        // Load platforms when tab is first opened
        setTimeout(() => {
            loadVideoGamePlatforms();
        }, 100);
    });

    // ===== END VIDEO GAME FILTER SYSTEM =====

    // Show welcome message when page loads
    showInfoToast('Welcome to Shopify Catalog Manager! Select items to push to your store.');
});
</script>
{% endblock %}
