def process_products(catalog_products, username, selected_conditions, output_dir, selected_languages):
    """
    Process products one expansion at a time, generating JSON files for each expansion
    before moving to the next one.
    """
    import os
    import json
    import logging
    from datetime import datetime

    logger = logging.getLogger(__name__)

    # Get user's title format settings
    title_format = current_user.get_title_format()
    logger.info(f"User {username} title format settings: {title_format}")

    # Initialize pricing calculator if saautopricing is available
    pricing_calculator = None
    user_currency = "USD"
    exchange_rate = 1.0
    tcgplayer_api_key = None

    if SAAUTOPRICING_AVAILABLE:
        try:
            # Get user profile for pricing settings
            user_profile = db['user'].find_one({'username': username})
            if user_profile:
                # Get user currency and exchange rate
                user_currency = user_profile.get('currency', 'USD')
                exchange_rate = get_exchange_rate(user_currency)
                logger.info(f"User currency: {user_currency}, Exchange rate: {exchange_rate}")

                # Get pricing settings
                settings = prepare_shopify_settings(user_profile)
                pricing_calculator = PricingCalculator(settings, user_currency)
                logger.info(f"Created pricing calculator with settings: {settings}")

                # Get TCGPlayer API key
                tcgplayer_key_doc = db['tcgplayerKey'].find_one({})
                if tcgplayer_key_doc:
                    tcgplayer_api_key = tcgplayer_key_doc.get('latestKey')
                    logger.info("Retrieved TCGPlayer API key")
                else:
                    logger.warning("TCGPlayer API key not found")
        except Exception as e:
            logger.error(f"Error initializing pricing calculator: {str(e)}")
            pricing_calculator = None

    # Get all unique expansions from the catalog products with their release dates
    expansion_info = {}
    for doc in catalog_products:
        expansion_name = doc.get("expansionName", "Unknown Expansion")
        release_date = doc.get("releasedOn", "")

        # Store the newest release date for each expansion
        if expansion_name not in expansion_info or (release_date and (not expansion_info[expansion_name] or release_date > expansion_info[expansion_name])):
            expansion_info[expansion_name] = release_date

    # Sort expansions by release date (newest first)
    sorted_expansions = sorted(expansion_info.keys(), key=lambda x: expansion_info[x] or "", reverse=True)
    logger.info(f"Sorted {len(sorted_expansions)} expansions by release date (newest first)")

    # Initialize counters
    file_count = 0
    batch_number = 1
    MAX_SIZE_MB = 10

    # Process each expansion one at a time
    for expansion_name in sorted_expansions:
        logger.info(f"Starting to process expansion: {expansion_name}")

        # Get all products for this expansion
        expansion_products = []
        for doc in catalog_products:
            if doc.get("expansionName") == expansion_name:
                expansion_products.append(doc)

        logger.info(f"Found {len(expansion_products)} products for expansion: {expansion_name}")

        # Skip if no products for this expansion
        if not expansion_products:
            logger.info(f"No products found for expansion: {expansion_name}, skipping")
            continue

        # Process all products for this expansion
        processed_products = []

        for doc in expansion_products:
            logger.info(f"Processing product {doc.get('productId')} - {doc.get('name')}")

            # Initialize metafield_games and metafield_legalities
            metafield_games = doc.get("metafieldGames", [])
            metafield_legalities = doc.get("metafieldLegalities", {})

            # Generate tags
            if doc.get("isSealed"):
                # For sealed items, only include game, abbreviation, and expansion name
                tags = [
                    doc.get("gameName", ""),
                    doc.get("abbreviation", ""),
                    doc.get("expansionName", "")
                ]
            else:
                # For singles, keep the original tag generation but exclude oracle text
                tags = [
                    doc.get("set_name", ""),
                    doc.get("rarity", ""),
                    doc.get("lang", ""),
                    doc.get("type_line", ""),
                    doc.get("gameName", ""),
                    doc.get("expansionName", ""),
                    doc.get("abbreviation", ""),
                    doc.get("language", "")
                ]

                # Add metafieldGames as individual tags
                tags.extend(metafield_games)

                # Add metafieldLegalities as tags
                for format, legality in metafield_legalities.items():
                    if legality.lower() == "legal":
                        tags.append(format)

            # Build body HTML with table structure
            body_html = '<table class="singles-description-table" xmlns="http://www.w3.org/1999/html"><tbody>'

            # Get extended data
            extended_data = doc.get("extendedData", [])
            extended_data_dict = {data.get('displayName', ''): data.get('value', '') for data in extended_data}

            # Add extendedData items as tags (except Description) - only for singles
            if not doc.get("isSealed"):
                for data in extended_data:
                    name = data.get('name', '')
                    value = data.get('value', '')

                    display_name = data.get('displayName', '')
                    # Skip Description, card_text, and Oracle Text fields
                    if name == "Description" or name == "card_text" or name == "Oracle Text" or display_name == "Oracle Text":
                        continue

                    # Add value as a tag if it exists
                    if value:
                        tags.append(value)

            # Remove empty tags and join
            tags = [tag for tag in tags if tag and tag.strip()]
            tags_string = ', '.join(tags)

            # Add all extended data to table
            if extended_data:
                for data in extended_data:
                    display_name = data.get('displayName', '')
                    value = data.get('value', '')
                    if display_name and value and display_name not in ['Oracle Text', 'Reverse Oracle Text', 'Reverse Type']:
                        body_html += f"""
      <tr>
          <td>{display_name}:</td>
          <td>{value}</td>
      </tr>"""

            # Add basic details if not already included in extended data
            basic_details = [
                ('Set', doc.get('expansionName', '')),
                ('Rarity', doc.get('rarity', '')),
                ('Number', doc.get('number', '')),
                ('Language', doc.get('language', ''))
            ]

            for label, value in basic_details:
                if value and label not in [data.get('displayName') for data in extended_data]:
                    body_html += f"""
      <tr>
          <td>{label}:</td>
          <td>{value}</td>
      </tr>"""

            body_html += """
</tbody>
</table>"""

            # Oracle text section
            oracle_text = extended_data_dict.get('Oracle Text', doc.get('description', ''))
            if oracle_text:
                body_html += f"""
<div class="single-description-div">
        <div class="oracle-text">
            {oracle_text}
        </div>
</div>"""

            # Check for reverse/back face data
            reverse_type = extended_data_dict.get('Reverse Type', '')
            reverse_oracle = extended_data_dict.get('Reverse Oracle Text', '')

            if reverse_type or reverse_oracle:
                # Add reverse side table
                body_html += """
<table class="singles-reverse-description-table">
<tbody>"""
                if reverse_type:
                    body_html += f"""
      <tr>
          <td>Reverse Type:</td>
          <td>{reverse_type}</td>
      </tr>"""
                body_html += """
</tbody>
</table>"""

                # Add reverse oracle text if available
                if reverse_oracle:
                    body_html += f"""
<div class="single-description-div">
        <div class="reverseOracle-text">
            {reverse_oracle}
        </div>
</div>"""

            # Add metadata div
            product_id = doc.get("productId", "N/A")
            game_name = doc.get('gameName', '')
            game_name_to_cardtype = {
                'Magic: The Gathering': 'mtg',
                'Pokemon': 'pokemon',
                'Yu-Gi-Oh!': 'yugioh',
                'Akora TCG': 'akora',
                'One Piece Card Game': 'onepiece'
            }
            data_cardtype = game_name_to_cardtype.get(game_name, 'other')

            body_html += f'''
        <div class="catalogMetaData" style="visibility: hidden;"
             data-cardtype="{data_cardtype}"
             data-cardid="5"
             data-tcgid="{product_id}"
             data-lastupdated="{datetime.now().isoformat()}">
        </div>
        '''

            # Construct title using user's format settings
            title = construct_title(doc, title_format)

            variants = []
            logger.info(f"Processing variants for product {doc.get('productId')} - {doc.get('name')}")

            # Ensure skus list exists
            skus = doc.get("skus", [])
            if not isinstance(skus, list):
                skus = []
                logger.warning(f"SKUs for product {doc.get('productId')} was not a list. Defaulting to empty list.")

            # Handle sealed items differently
            if doc.get("isSealed"):
                logger.info(f"Processing sealed item: {doc.get('productId')} - {doc.get('name')}")
                # For sealed items, create a single variant without condition/language filtering

                # Use the product ID as SKU ID if no SKUs exist
                sku_id = doc.get("productId")
                if skus and isinstance(skus[0], dict):
                    sku_id = skus[0].get("skuId", sku_id)
                    low_price = skus[0].get("lowPrice", 0.00)
                else:
                    low_price = 0.00

                # Format price
                try:
                    price = "{:.2f}".format(float(low_price))
                except (ValueError, TypeError):
                    price = "0.00"

                # Extract UPC/barcode from top-level fields for barcode
                upc_barcode = doc.get('upc', '') or doc.get('barcode', '')
                if upc_barcode:
                    upc_barcode = str(upc_barcode)

                # Use UPC as barcode if available, otherwise fall back to SKU ID
                barcode_value = upc_barcode if upc_barcode else str(sku_id)

                logger.info(f"Creating sealed variant with price: {price}, barcode: {barcode_value}")
                variants.append({
                    "title": "Sealed",  # Simple title for sealed items
                    "price": price,
                    "sku": str(sku_id),  # Keep SKU as skuId
                    "barcode": barcode_value,  # Use UPC if available, otherwise skuId
                    "weight": 0.3,
                    "weightUnit": "GRAMS",
                    "options": ["Sealed"],
                    "requiresShipping": True,
                    "inventoryManagement": "SHOPIFY",
                    "inventoryPolicy": "DENY",
                    "condition_order": 1  # All sealed items get same condition order
                })
            else:
                # Process singles with condition filtering
                for sku in skus:
                    if not isinstance(sku, dict):
                        continue

                    # Map languageId to language abbreviation
                    language_map = {
                        1: "EN",
                        # Add other language mappings as needed
                    }
                    lang_abbr = language_map.get(sku.get("languageId"), sku.get("langAbbr", "EN"))

                    # Skip if languages are selected and this language isn't in the selection
                    if selected_languages and selected_languages != [] and lang_abbr not in selected_languages:
                        logger.info(f"Skipping language {lang_abbr} - not in selected languages")
                        continue

                    # Map conditionId to condition name
                    condition_id_map = {
                        1: "Near Mint",
                        2: "Lightly Played",
                        3: "Moderately Played",
                        4: "Heavily Played",
                        5: "Damaged"
                    }
                    condition = condition_id_map.get(sku.get('conditionId'), sku.get('condName', ''))

                    # Special handling for user Dezmu - filter conditions first
                    if username == "Dezmu":
                        if condition not in ["Near Mint", "Lightly Played"]:
                            logger.info(f"Skipping condition {condition} for user Dezmu")
                            continue
                    # Special handling for user Xavier
                    elif username == "Xavier":
                        if condition != "Near Mint":
                            logger.info(f"Skipping condition {condition} for user Xavier")
                            continue

                    # After user-specific filtering, check if condition is in selected conditions
                    if condition not in selected_conditions:
                        logger.info(f"Skipping condition {condition} - not in selected conditions")
                        continue

                    printing = sku.get('printingName', '')
                    # Format variant title like warehouse:
                    # If printing is Normal, just show condition
                    # If printing is special (Foil etc), show "condition - printing"
                    if printing.lower() == 'normal':
                        variant_title = condition
                    else:
                        variant_title = f"{condition} - {printing}"

                    # Calculate price using saautopricing if available
                    if pricing_calculator and tcgplayer_api_key:
                        try:
                            # Get product ID
                            product_id = str(doc.get('productId'))

                            # Fetch pricing data from TCGPlayer API if not already fetched
                            if not hasattr(process_products, 'pricing_data'):
                                process_products.pricing_data = {}

                            if product_id not in process_products.pricing_data:
                                product_pricing = fetch_pricing_data([product_id], tcgplayer_api_key)
                                process_products.pricing_data.update(product_pricing)

                            # Get pricing data for this product
                            product_pricing = process_products.pricing_data.get(product_id, [])

                            # Only include subtypes that have valid prices
                            valid_subtypes = [p.get('subTypeName') for p in product_pricing if p.get('subTypeName')]

                            # Determine printing type
                            printing_type = determine_printing_type(variant_title, valid_subtypes)

                            # Find the price data for this printing type
                            matched_price = None
                            for p in product_pricing:
                                if p.get('subTypeName', '').lower() == printing_type.lower():
                                    matched_price = p
                                    break

                            if matched_price:
                                # Extract pricing info for this printing type and convert from USD to user currency
                                pricing_info = {}

                                # Get market price
                                market_price = matched_price.get('marketPrice')
                                if market_price is not None:
                                    pricing_info['marketPrice'] = float(market_price) * exchange_rate

                                # Get low price
                                low_price = matched_price.get('lowPrice')
                                if low_price is not None:
                                    pricing_info['lowPrice'] = float(low_price) * exchange_rate

                                # Get mid price
                                mid_price = matched_price.get('midPrice')
                                if mid_price is not None:
                                    pricing_info['midPrice'] = float(mid_price) * exchange_rate

                                # Get high price
                                high_price = matched_price.get('highPrice')
                                if high_price is not None:
                                    pricing_info['highPrice'] = float(high_price) * exchange_rate

                                # Create sku_info for price calculation
                                sku_info = {
                                    'pricingInfo': pricing_info,
                                    'condName': condition,
                                    'printingName': printing_type,
                                    'skuId': sku.get('skuId'),
                                    'variantTitle': variant_title
                                }

                                # Calculate price
                                calculated_price, is_missing, price_history = pricing_calculator.calculate_final_price(sku_info, doc)

                                if not is_missing and calculated_price is not None:
                                    price = "{:.2f}".format(calculated_price)
                                    logger.info(f"Calculated price for {variant_title}: {price} (using saautopricing)")
                                else:
                                    # Fall back to low price if calculation fails
                                    low_price = sku.get("lowPrice")
                                    if low_price is None:
                                        price = "0.00"
                                    else:
                                        try:
                                            price = "{:.2f}".format(float(low_price))
                                        except (ValueError, TypeError):
                                            price = "0.00"
                                    logger.info(f"Using fallback price for {variant_title}: {price} (calculation failed)")
                            else:
                                # Fall back to low price if no matching price data
                                low_price = sku.get("lowPrice")
                                if low_price is None:
                                    price = "0.00" # Default before checking prices collection
                                else:
                                    try:
                                        price = "{:.2f}".format(float(low_price))
                                    except (ValueError, TypeError):
                                        price = "0.00" # Default before checking prices collection
                                logger.info(f"Using fallback price for {variant_title}: {price} (no matching price data)")
                        except Exception as e:
                            # Fall back to low price if an error occurs
                            logger.error(f"Error calculating price for {variant_title}: {str(e)}")
                            low_price = sku.get("lowPrice")
                            if low_price is None:
                                price = "0.00" # Default before checking prices collection
                            else:
                                try:
                                    price = "{:.2f}".format(float(low_price))
                                except (ValueError, TypeError):
                                    price = "0.00" # Default before checking prices collection
                            logger.info(f"Using fallback price for {variant_title}: {price} (error occurred)")
                    else:
                        # Use low price if saautopricing is not available
                        low_price = sku.get("lowPrice")
                        if low_price is None:
                            logger.warning(f"Low price is None in SKU for product {doc.get('productId')} - {doc.get('name')}. Will check prices collection.")
                            price = "0.00" # Default before checking prices collection
                        else:
                            try:
                                price = "{:.2f}".format(float(low_price))
                            except (ValueError, TypeError) as e:
                                logger.error(f"Error processing SKU price for product {doc.get('productId')} - {doc.get('name')}: {str(e)}. Will check prices collection.")
                                price = "0.00" # Default before checking prices collection

                    # Fallback: If price is still "0.00", try fetching from the 'prices' collection
                    if price == "0.00":
                        try:
                            product_id_int = int(doc.get('productId'))
                            prices_collection = db['prices']
                            price_doc = prices_collection.find_one({'productId': product_id_int})

                            if price_doc and 'low' in price_doc and price_doc['low'] is not None:
                                fallback_price_val = price_doc['low']
                                try:
                                    price = "{:.2f}".format(float(fallback_price_val))
                                    logger.info(f"Using fallback price from 'prices' collection for {variant_title}: {price}")
                                except (ValueError, TypeError):
                                    logger.warning(f"Invalid price format in 'prices' collection for productId {product_id_int}. Keeping price as 0.00.")
                                    price = "0.00"
                            else:
                                logger.warning(f"No valid price found in 'prices' collection for productId {product_id_int}. Keeping price as 0.00.")
                                price = "0.00"

                        except ValueError:
                             logger.error(f"Could not convert productId {doc.get('productId')} to int for prices collection lookup.")
                             price = "0.00"
                        except Exception as e_prices:
                            logger.error(f"Error querying 'prices' collection for productId {doc.get('productId')}: {str(e_prices)}")
                            price = "0.00"

                    # Extract UPC/barcode from top-level fields for barcode
                    upc_barcode = doc.get('upc', '') or doc.get('barcode', '')
                    if upc_barcode:
                        upc_barcode = str(upc_barcode)

                    # Use UPC as barcode if available, otherwise fall back to SKU ID
                    barcode_value = upc_barcode if upc_barcode else str(sku.get("skuId", ""))

                    logger.info(f"Creating variant with title: {variant_title}, final price: {price}, barcode: {barcode_value}")
                    variants.append({
                        "title": variant_title,
                        "price": price,
                        "sku": str(sku.get("skuId", "")),  # Keep SKU as skuId
                        "barcode": barcode_value,  # Use UPC if available, otherwise skuId
                        "weight": 0.3,
                        "weightUnit": "GRAMS",
                        "options": [variant_title],
                        "requiresShipping": True,
                        "inventoryManagement": "SHOPIFY",
                        "inventoryPolicy": "DENY",
                        "condition_order": get_condition_order(condition)
                    })

            # Skip products with no variants after filtering
            if not variants:
                logger.info(f"Skipping product {doc.get('productId')} - {doc.get('name')} - no valid variants after filtering")
                continue

            logger.info(f"Final variants count for product {doc.get('productId')}: {len(variants)}")

            # Sort variants by printing (Normal first, then Foil) and then by condition order
            variants.sort(key=lambda x: (
                0 if x['title'].startswith("Normal") else 1,
                x['condition_order']
            ))

            # Remove the temporary 'condition_order' key
            for variant in variants:
                del variant['condition_order']

            product_status = "DRAFT" if any(variant["price"] == "0.00" for variant in variants) else "ACTIVE"

            if doc.get('gameName') == "Magic: The Gathering":
                product_type = "MTG Single" if doc.get('isSingle') else "MTG Sealed"
            else:
                product_type = f"{doc.get('gameName', 'Unknown')} {'Single' if doc.get('isSingle') else 'Sealed'}"

            # Create metafields list starting with game
            # Ensure gameName is included in metafield_games
            game_name = doc.get("gameName", "")
            if game_name and game_name not in metafield_games:
                metafield_games.append(game_name)

            metafields = []  # Removed all metafields as requested

            product_data = {
                "input": {
                    "title": title,
                    "published": True,
                    "status": "ACTIVE",
                    "publishedAt": datetime.now().strftime("%Y-%m-%d"),
                    "tags": tags_string,
                    "bodyHtml": body_html,
                    "vendor": doc.get("gameName", "Unknown Vendor"),
                    "productType": product_type,
                    "variants": variants,
                    "options": ["Title"],
                    "metafields": metafields
                },
                "media": [
                    {
                        "originalSource": doc.get("image", "No Image"),
                        "alt": f"Image for {title} - {doc.get('gameName', 'Unknown Game')}",
                        "mediaContentType": "IMAGE"
                    }
                ]
            }

            processed_products.append(product_data)

        # After processing all products for this expansion, save to JSON file
        if processed_products:
            # Create a sanitized expansion name for the filename
            safe_expansion_name = ''.join(c if c.isalnum() else '_' for c in expansion_name)

            # Split into batches if needed to stay under 10MB limit
            current_batch = []
            expansion_batch_number = 1

            for product in processed_products:
                current_batch.append(product)

                # Check if batch would exceed size limit
                test_json = json.dumps(current_batch, ensure_ascii=False, indent=2)
                current_size_mb = len(test_json.encode('utf-8')) / (1024 * 1024)

                # If adding this product would exceed the limit, save the batch (without this product)
                if current_size_mb > MAX_SIZE_MB:
                    # Remove the last product that pushed it over the limit
                    last_product = current_batch.pop()

                    # Save current batch
                    if current_batch:  # Only save if there's something to save
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        filename = f"{safe_expansion_name}_batch{expansion_batch_number}_{timestamp}.json"
                        filepath = os.path.join(output_dir, filename)

                        # Create directory if it doesn't exist
                        if not os.path.exists(output_dir):
                            os.makedirs(output_dir)

                        # Convert to JSON string and save
                        json_data = json.dumps(current_batch, ensure_ascii=False, indent=2)
                        with open(filepath, 'w', encoding='utf-8') as f:
                            f.write(json_data)

                        logger.info(f"Saved batch {expansion_batch_number} for expansion {expansion_name}: {filepath}")
                        file_count += 1
                        expansion_batch_number += 1
                        batch_number += 1

                    # Start a new batch with the product that was removed
                    current_batch = [last_product]

            # Save any remaining products in this expansion
            if current_batch:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{safe_expansion_name}_batch{expansion_batch_number}_{timestamp}.json"
                filepath = os.path.join(output_dir, filename)

                # Create directory if it doesn't exist
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                # Convert to JSON string and save
                json_data = json.dumps(current_batch, ensure_ascii=False, indent=2)
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(json_data)

                logger.info(f"Saved final batch {expansion_batch_number} for expansion {expansion_name}: {filepath}")
                file_count += 1

            # Add a clear message that we've completed processing this expansion
            logger.info(f"COMPLETED PROCESSING EXPANSION: {expansion_name} - Generated {expansion_batch_number} JSON files")

    logger.info(f"All expansions processed and saved to {file_count} JSON files.")
    return {"message": f"{file_count} JSON files generated successfully. Check the directory: {output_dir}", "type": "success"}
