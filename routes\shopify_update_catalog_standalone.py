#!/usr/bin/env python3

import os
import json
import logging
import argparse
import traceback
from datetime import datetime
from pymongo import MongoClient, ASCENDING
from pymongo.errors import OperationFailure

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MongoDB connection
mongo_uri = '*******************************************************************'

# Default settings
DEFAULT_PRICING = {
    'Near Mint': 100,  # 100%
    'Lightly Played': 80,  # 80%
    'Moderately Played': 70,  # 70%
    'Heavily Played': 60,  # 60%
    'Damaged': 50  # 50%
}

# Title format settings
TITLE_FORMAT = {
    'includeName': True,
    'includeNumber': True,
    'includeExpansion': True,
    'includeAbbreviation': False,
    'order': ['name', 'number', 'expansion']
}

# Only use English language
SELECTED_LANGUAGES = ['EN']

def get_available_games(catalog_collection):
    """Get all available games from the catalog"""
    games = catalog_collection.distinct('gameName')
    return sorted([game for game in games if game and game.strip()])

def get_expansions_for_game(catalog_collection, game_name):
    """Get all expansions for a specific game"""
    expansions = catalog_collection.distinct('expansionName', {'gameName': game_name})
    return sorted([exp for exp in expansions if exp])

def get_products_for_expansion(catalog_collection, game_name, expansion_name):
    """Get all products for a specific game and expansion"""
    query = {
        'gameName': game_name,
        'expansionName': expansion_name
    }
    
    # Get all products for this expansion
    pipeline = [
        {"$match": query},
        {"$sort": {"name": 1}},
        {"$project": {
            "_id": 0,
            "productId": 1,
            "idProduct": 1,
            "name": 1,
            "expansionName": 1,
            "gameName": 1,
            "set_name": 1,
            "rarity": 1,
            "lang": 1,
            "type_line": 1,
            "oracle_text": 1,
            "abbreviation": 1,
            "language": 1,
            "metafieldGames": 1,
            "metafieldLegalities": 1,
            "extendedData": 1,
            "description": 1,
            "number": 1,
            "isSingle": 1,
            "isSealed": 1,
            "image": 1,
            "skus": 1,
            "upc": 1,  # Add UPC field
            "barcode": 1  # Add barcode field
        }}
    ]
    
    return list(catalog_collection.aggregate(pipeline))

def construct_title(doc, title_format):
    """Construct a title based on the specified format"""
    # For sealed items, just use the name directly without any modifications
    if doc.get("isSealed"):
        return doc.get('name', 'No Title')

    # For singles, apply the title formatting
    # Get raw values first
    raw_name = doc.get('name', 'No Title')
    raw_number = str(doc.get('number', ''))  # Convert to string and handle None case
    raw_abbreviation = doc.get('abbreviation')

    # Get expansion name
    expansion_name = doc.get('expansionName', '')

    # Format components
    components = {
        'name': raw_name,
        'number': f"({raw_number})" if raw_number else None,
        'abbreviation': f"({raw_abbreviation})" if raw_abbreviation else None,
        'expansion': f"({expansion_name})" if expansion_name else None
    }

    # Build title based on format preferences
    title_parts = []

    # Use user-defined order and inclusion settings
    for part in title_format.get('order', ['name', 'number', 'expansion', 'abbreviation']):
        include_key = f'include{part.capitalize()}'
        if title_format.get(include_key, True) and components.get(part):
            title_parts.append(components[part])

    return ' '.join(title_parts)  # Join with space

def get_condition_order(condition):
    """Get the order of conditions for sorting"""
    order = {
        "Near Mint": 1,
        "Lightly Played": 2,
        "Moderately Played": 3,
        "Heavily Played": 4,
        "Damaged": 5
    }
    return order.get(condition, 6)  # Unknown conditions go at the end

def get_prices_from_prices_collection(db, product_ids):
    """Get prices from the prices collection for a batch of product IDs"""
    prices_collection = db['prices']
    prices_data = {}
    
    try:
        # Convert product IDs to integers for querying
        int_product_ids = []
        for pid in product_ids:
            try:
                int_product_ids.append(int(pid))
            except (ValueError, TypeError):
                logger.warning(f"Could not convert product ID {pid} to integer")
        
        if not int_product_ids:
            logger.warning("No valid integer product IDs to query")
            return prices_data
            
        # Query the prices collection for all product IDs at once
        logger.info(f"Querying prices collection for {len(int_product_ids)} product IDs")
        cursor = prices_collection.find({"productId": {"$in": int_product_ids}})
        
        # Process the results
        for doc in cursor:
            product_id = doc.get('productId')
            if product_id is not None and 'prices' in doc:
                # The prices are stored in a nested 'prices' object with printing types as keys
                prices_obj = doc.get('prices', {})
                
                # Try to get the 'Normal' printing type first, fall back to any available printing type
                if 'Normal' in prices_obj and 'lowPrice' in prices_obj['Normal']:
                    try:
                        low_price = prices_obj['Normal']['lowPrice']
                        if low_price is not None:
                            prices_data[str(product_id)] = float(low_price)
                            logger.debug(f"Found Normal price for product {product_id}: {low_price}")
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid Normal price format for productId {product_id}")
                else:
                    # If 'Normal' is not available, use the first available printing type
                    for printing_type, price_data in prices_obj.items():
                        if 'lowPrice' in price_data:
                            try:
                                low_price = price_data['lowPrice']
                                if low_price is not None:
                                    prices_data[str(product_id)] = float(low_price)
                                    logger.debug(f"Found {printing_type} price for product {product_id}: {low_price}")
                                    break
                            except (ValueError, TypeError):
                                logger.warning(f"Invalid {printing_type} price format for productId {product_id}")
    
    except Exception as e:
        logger.error(f"Error fetching prices from prices collection: {str(e)}")
    
    return prices_data

def process_products_for_expansion(products, default_pricing, title_format, selected_languages, db):
    """Process products for a specific expansion and generate JSON data"""
    processed_products = []
    
    # Get all product IDs for this batch
    product_ids = [str(doc.get('productId')) for doc in products if doc.get('productId')]
    
    # Get prices from the prices collection
    prices_data = get_prices_from_prices_collection(db, product_ids)
    logger.info(f"Retrieved {len(prices_data)} prices from prices collection")
    
    for doc in products:
        # Initialize metafield_games and metafield_legalities
        metafield_games = doc.get("metafieldGames", [])
        metafield_legalities = doc.get("metafieldLegalities", {})
        
        # Generate tags
        if doc.get("isSealed"):
            # For sealed items, only include game, abbreviation, and expansion name
            tags = [
                doc.get("gameName", ""),
                doc.get("abbreviation", ""),
                doc.get("expansionName", "")
            ]
        else:
            # For singles, keep the original tag generation but exclude oracle text
            tags = [
                doc.get("set_name", ""),
                doc.get("rarity", ""),
                doc.get("lang", ""),
                doc.get("type_line", ""),
                doc.get("gameName", ""),
                doc.get("expansionName", ""),
                doc.get("abbreviation", ""),
                doc.get("language", "")
            ]
            
            # Add metafieldGames as individual tags
            tags.extend(metafield_games)
            
            # Add metafieldLegalities as tags
            for format, legality in metafield_legalities.items():
                if legality.lower() == "legal":
                    tags.append(format)
        
        # Build body HTML with table structure
        body_html = '<table class="singles-description-table" xmlns="http://www.w3.org/1999/html"><tbody>'
        
        # Get extended data
        extended_data = doc.get("extendedData", [])
        extended_data_dict = {data.get('displayName', ''): data.get('value', '') for data in extended_data}
        
        # Add extendedData items as tags (except Description) - only for singles
        if not doc.get("isSealed"):
            for data in extended_data:
                name = data.get('name', '')
                value = data.get('value', '')
                
                display_name = data.get('displayName', '')
                # Skip Description, card_text, and Oracle Text fields
                if name == "Description" or name == "card_text" or name == "Oracle Text" or display_name == "Oracle Text":
                    continue
                
                # Add value as a tag if it exists
                if value:
                    tags.append(value)
        
        # Remove empty tags and join
        tags = [tag for tag in tags if tag and tag.strip()]
        tags_string = ', '.join(tags)
        
        # Add all extended data to table
        if extended_data:
            for data in extended_data:
                display_name = data.get('displayName', '')
                value = data.get('value', '')
                if display_name and value and display_name not in ['Oracle Text', 'Reverse Oracle Text', 'Reverse Type']:
                    body_html += f"""
      <tr>
          <td>{display_name}:</td>
          <td>{value}</td>
      </tr>"""
        
        # Add basic details if not already included in extended data
        basic_details = [
            ('Set', doc.get('expansionName', '')),
            ('Rarity', doc.get('rarity', '')),
            ('Number', doc.get('number', '')),
            ('Language', doc.get('language', ''))
        ]
        
        for label, value in basic_details:
            if value and label not in [data.get('displayName') for data in extended_data]:
                body_html += f"""
      <tr>
          <td>{label}:</td>
          <td>{value}</td>
      </tr>"""
        
        body_html += """
</tbody>
</table>"""
        
        # Oracle text section
        oracle_text = extended_data_dict.get('Oracle Text', doc.get('description', ''))
        if oracle_text:
            body_html += f"""
<div class="single-description-div">
        <div class="oracle-text">
            {oracle_text}
        </div>
</div>"""
        
        # Check for reverse/back face data
        reverse_type = extended_data_dict.get('Reverse Type', '')
        reverse_oracle = extended_data_dict.get('Reverse Oracle Text', '')
        
        if reverse_type or reverse_oracle:
            # Add reverse side table
            body_html += """
<table class="singles-reverse-description-table">
<tbody>"""
            if reverse_type:
                body_html += f"""
      <tr>
          <td>Reverse Type:</td>
          <td>{reverse_type}</td>
      </tr>"""
            body_html += """
</tbody>
</table>"""
            
            # Add reverse oracle text if available
            if reverse_oracle:
                body_html += f"""
<div class="single-description-div">
        <div class="reverseOracle-text">
            {reverse_oracle}
        </div>
</div>"""
        
        # Add metadata div
        product_id = doc.get("productId", "N/A")
        game_name = doc.get('gameName', '')
        game_name_to_cardtype = {
            'Magic: The Gathering': 'mtg',
            'Pokemon': 'pokemon',
            'Yu-Gi-Oh!': 'yugioh',
            'Akora TCG': 'akora',
            'One Piece Card Game': 'onepiece'
        }
        data_cardtype = game_name_to_cardtype.get(game_name, 'other')
        
        body_html += f'''
        <div class="catalogMetaData" style="visibility: hidden;"
             data-cardtype="{data_cardtype}"
             data-cardid="5"
             data-tcgid="{product_id}"
             data-lastupdated="{datetime.now().isoformat()}">
        </div>
        '''
        
        # Construct title using format settings
        title = construct_title(doc, title_format)
        
        variants = []
        
        # Ensure skus list exists
        skus = doc.get("skus", [])
        if not isinstance(skus, list):
            skus = []
        
        # Handle sealed items differently
        if doc.get("isSealed"):
            # For sealed items, create a single variant without condition/language filtering
            
            # Use the product ID as SKU ID if no SKUs exist
            sku_id = doc.get("productId")
            if skus and isinstance(skus[0], dict):
                sku_id = skus[0].get("skuId", sku_id)
            
            # Get price from prices collection first, fall back to SKU price if not available
            product_id_str = str(doc.get('productId', ''))
            if product_id_str in prices_data:
                base_price = prices_data[product_id_str]
                logger.debug(f"Using price from prices collection for sealed product {product_id_str}: {base_price}")
            else:
                # Fall back to SKU price if not in prices collection
                if skus and isinstance(skus[0], dict):
                    low_price = skus[0].get("lowPrice", 0.00)
                else:
                    low_price = 0.00
                
                try:
                    base_price = float(low_price)
                except (ValueError, TypeError):
                    base_price = 0.00
                logger.debug(f"Using SKU price for sealed product {product_id_str}: {base_price}")
            
            # Format price
            price = "{:.2f}".format(base_price)
            
            variants.append({
                "title": "Sealed",  # Simple title for sealed items
                "price": price,
                "sku": str(sku_id),
                "barcode": str(sku_id),
                "weight": 0.3,
                "weightUnit": "GRAMS",
                "options": ["Sealed"],
                "requiresShipping": True,
                "inventoryManagement": "SHOPIFY",
                "inventoryPolicy": "DENY",
                "condition_order": 1  # All sealed items get same condition order
            })
        else:
            # Process singles with condition filtering
            for sku in skus:
                if not isinstance(sku, dict):
                    continue
                
                # Map languageId to language abbreviation
                language_map = {
                    1: "EN",
                    # Add other language mappings as needed
                }
                lang_abbr = language_map.get(sku.get("languageId"), sku.get("langAbbr", "EN"))
                
                # Skip if languages are selected and this language isn't in the selection
                if selected_languages and selected_languages != [] and lang_abbr not in selected_languages:
                    continue
                
                # Map conditionId to condition name
                condition_id_map = {
                    1: "Near Mint",
                    2: "Lightly Played",
                    3: "Moderately Played",
                    4: "Heavily Played",
                    5: "Damaged"
                }
                condition = condition_id_map.get(sku.get('conditionId'), sku.get('condName', ''))
                
                printing = sku.get('printingName', '')
                # Format variant title like warehouse:
                # If printing is Normal, just show condition
                # If printing is special (Foil etc), show "condition - printing"
                if printing.lower() == 'normal':
                    variant_title = condition
                else:
                    variant_title = f"{condition} - {printing}"
                
                # Get base price from prices collection first, fall back to SKU price if not available
                product_id_str = str(doc.get('productId', ''))
                if product_id_str in prices_data:
                    base_price = prices_data[product_id_str]
                    logger.debug(f"Using price from prices collection for product {product_id_str}: {base_price}")
                else:
                    # Fall back to SKU price if not in prices collection
                    low_price = sku.get("lowPrice", 0.00)
                    try:
                        base_price = float(low_price)
                    except (ValueError, TypeError):
                        base_price = 0.00
                    logger.debug(f"Using SKU price for product {product_id_str}: {base_price}")
                
                # Apply condition-based pricing
                condition_percentage = default_pricing.get(condition, 100) / 100.0
                final_price = base_price * condition_percentage
                
                # Format price
                price = "{:.2f}".format(final_price)
                
                # Extract UPC/barcode from top-level fields for barcode
                upc_barcode = doc.get('upc', '') or doc.get('barcode', '')
                if upc_barcode:
                    upc_barcode = str(upc_barcode)

                # Use UPC as barcode if available, otherwise fall back to SKU ID
                barcode_value = upc_barcode if upc_barcode else str(sku.get("skuId", ""))

                variants.append({
                    "title": variant_title,
                    "price": price,
                    "sku": str(sku.get("skuId", "")),  # Keep SKU as skuId
                    "barcode": barcode_value,  # Use UPC if available, otherwise skuId
                    "weight": 0.3,
                    "weightUnit": "GRAMS",
                    "options": [variant_title],
                    "requiresShipping": True,
                    "inventoryManagement": "SHOPIFY",
                    "inventoryPolicy": "DENY",
                    "condition_order": get_condition_order(condition)
                })
        
        # Skip products with no variants after filtering
        if not variants:
            continue
        
        # Sort variants by printing (Normal first, then Foil) and then by condition order
        variants.sort(key=lambda x: (
            0 if x['title'].startswith("Normal") else 1,
            x['condition_order']
        ))
        
        # Remove the temporary 'condition_order' key
        for variant in variants:
            del variant['condition_order']
        
        if doc.get('gameName') == "Magic: The Gathering":
            product_type = "MTG Single" if doc.get('isSingle') else "MTG Sealed"
        else:
            product_type = f"{doc.get('gameName', 'Unknown')} {'Single' if doc.get('isSingle') else 'Sealed'}"
        
        # Create metafields list starting with game
        # Ensure gameName is included in metafield_games
        game_name = doc.get("gameName", "")
        if game_name and game_name not in metafield_games:
            metafield_games.append(game_name)
        
        metafields = []  # Removed all metafields as requested
        
        product_data = {
            "input": {
                "title": title,
                "published": True,
                "status": "ACTIVE",
                "publishedAt": datetime.now().strftime("%Y-%m-%d"),
                "tags": tags_string,
                "bodyHtml": body_html,
                "vendor": doc.get("gameName", "Unknown Vendor"),
                "productType": product_type,
                "variants": variants,
                "options": ["Title"],
                "metafields": metafields
            },
            "media": [
                {
                    "originalSource": doc.get("image", "No Image"),
                    "alt": f"Image for {title} - {doc.get('gameName', 'Unknown Game')}",
                    "mediaContentType": "IMAGE"
                }
            ]
        }
        
        processed_products.append(product_data)
    
    return processed_products

def save_json_files(products_by_expansion, output_dir):
    """Save products to JSON files by expansion"""
    file_count = 0
    MAX_SIZE_MB = 10
    
    # Process each expansion separately
    for expansion_name, expansion_products in products_by_expansion.items():
        if not expansion_products:
            continue
        
        # Create a sanitized expansion name for the filename
        safe_expansion_name = ''.join(c if c.isalnum() else '_' for c in expansion_name)
        
        # Split expansion products into batches if needed to stay under 10MB limit
        current_batch = []
        expansion_batch_number = 1
        
        for product in expansion_products:
            current_batch.append(product)
            
            # Check if batch would exceed size limit
            test_json = json.dumps(current_batch, ensure_ascii=False, indent=2)
            current_size_mb = len(test_json.encode('utf-8')) / (1024 * 1024)
            
            # If adding this product would exceed the limit, save the batch (without this product)
            if current_size_mb > MAX_SIZE_MB:
                # Remove the last product that pushed it over the limit
                last_product = current_batch.pop()
                
                # Save current batch
                if current_batch:  # Only save if there's something to save
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    filename = f"{safe_expansion_name}_batch{expansion_batch_number}_{timestamp}.json"
                    filepath = os.path.join(output_dir, filename)
                    
                    # Create directory if it doesn't exist
                    if not os.path.exists(output_dir):
                        os.makedirs(output_dir)
                    
                    # Convert to JSON string and save
                    json_data = json.dumps(current_batch, ensure_ascii=False, indent=2)
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(json_data)
                    
                    file_count += 1
                    expansion_batch_number += 1
                
                # Start a new batch with the product that was removed
                current_batch = [last_product]
        
        # Save any remaining products in this expansion
        if current_batch:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_expansion_name}_batch{expansion_batch_number}_{timestamp}.json"
            filepath = os.path.join(output_dir, filename)
            
            # Create directory if it doesn't exist
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # Convert to JSON string and save
            json_data = json.dumps(current_batch, ensure_ascii=False, indent=2)
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(json_data)
            
            file_count += 1
    
    return file_count

def main():
    """Main execution function"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate Shopify JSON files for TCG products')
    parser.add_argument('--game', help='Specific game to process (optional)')
    parser.add_argument('--expansion', help='Specific expansion to process (optional)')
    parser.add_argument('--all-expansions', action='store_true', help='Process all expansions for the specified game')
    parser.add_argument('--output-dir', default='shopify_json_files', help='Output directory for JSON files')
    args = parser.parse_args()
    
    try:
        # Connect to MongoDB
        logger.info("Connecting to MongoDB...")
        mongo_client = MongoClient(mongo_uri)
        db = mongo_client['test']
        catalog_collection = db['catalog']
        
        # Get available games
        if args.game:
            games = [args.game]
            logger.info(f"Processing specific game: {args.game}")
        else:
            games = get_available_games(catalog_collection)
            logger.info(f"Found {len(games)} games to process")
        
        total_files_created = 0
        
        # Process each game
        for game in games:
            logger.info(f"Processing game: {game}")
            
            # Create game-specific output directory
            game_dir = os.path.join(args.output_dir, game.replace(':', '').replace(' ', '_'))
            if not os.path.exists(game_dir):
                os.makedirs(game_dir)
            
            # Get expansions for this game
            if args.expansion and args.game == game:
                expansions = [args.expansion]
                logger.info(f"Processing specific expansion: {args.expansion}")
            else:
                expansions = get_expansions_for_game(catalog_collection, game)
                logger.info(f"Found {len(expansions)} expansions for {game}")
            
            # Process each expansion
            for expansion in expansions:
                logger.info(f"Processing expansion: {expansion}")
                
                # Get products for this expansion
                products = get_products_for_expansion(catalog_collection, game, expansion)
                logger.info(f"Found {len(products)} products for {expansion}")
                
                if not products:
                    logger.info(f"No products found for {expansion}, skipping")
                    continue
                
                # Process products
                processed_products = process_products_for_expansion(
                    products, 
                    DEFAULT_PRICING, 
                    TITLE_FORMAT, 
                    SELECTED_LANGUAGES,
                    db
                )
                
                logger.info(f"Processed {len(processed_products)} products for {expansion}")
                
                if not processed_products:
                    logger.info(f"No processed products for {expansion}, skipping")
                    continue
                
                # Organize products by expansion
                products_by_expansion = {expansion: processed_products}
                
                # Save JSON files
                files_created = save_json_files(products_by_expansion, game_dir)
                total_files_created += files_created
                
                logger.info(f"Created {files_created} JSON files for {expansion}")
        
        logger.info(f"Processing complete. Total JSON files created: {total_files_created}")
        
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        traceback.print_exc()
    finally:
        # Close MongoDB connection
        if 'mongo_client' in locals():
            mongo_client.close()

if __name__ == "__main__":
    main()
