{% extends "base.html" %}
{% block title %}Customers Dashboard{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
</style>
<div class="container mt-5">
    <div class="row">
        <!-- Shopify Customers Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(46, 204, 113, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fab fa-shopify fa-2x" style="color: #2ecc71;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Shopify Customers</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">View and manage your Shopify customers. Access customer details, order history, and spending analytics from your Shopify store.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('shopify_customers.get_customers') }}" class="btn w-100" style="background-color: #2ecc71; border-color: #2ecc71; color: white;">
                            <i class="fab fa-shopify me-2"></i>View Shopify Customers
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Analytics Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(52, 152, 219, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-line fa-2x" style="color: #3498db;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Customer Analytics</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Advanced customer analytics and insights. Track customer lifetime value, purchase patterns, and segmentation data.</p>
                    <div class="mt-auto">
                        <a href="#" class="btn w-100" style="background-color: #3498db; border-color: #3498db; color: white; pointer-events: none; opacity: 0.6;">
                            <i class="fas fa-chart-line me-2"></i>View Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Customer Segments Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(155, 89, 182, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-users fa-2x" style="color: #9b59b6;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Customer Segments</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Create and manage customer segments based on purchase behavior, demographics, and engagement levels for targeted marketing.</p>
                    <div class="mt-auto">
                        <a href="#" class="btn w-100" style="background-color: #9b59b6; border-color: #9b59b6; color: white; pointer-events: none; opacity: 0.6;">
                            <i class="fas fa-users me-2"></i>Manage Segments
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Communication Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(243, 156, 18, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-envelope fa-2x" style="color: #f39c12;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Customer Communication</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Send targeted emails, newsletters, and promotional campaigns to your customers. Track engagement and response rates.</p>
                    <div class="mt-auto">
                        <a href="#" class="btn w-100" style="background-color: #f39c12; border-color: #f39c12; color: white; pointer-events: none; opacity: 0.6;">
                            <i class="fas fa-envelope me-2"></i>Send Communications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Store Credit Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100 card-locked" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="coming-soon-badge" style="position: absolute; top: 10px; right: 10px; background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.7rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.5px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); z-index: 10;">Coming Soon</div>
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(26, 188, 156, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-credit-card fa-2x" style="color: #1abc9c;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Store Credit</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manage customer store credit balances, issue refunds as store credit, and track credit usage across all customer transactions.</p>
                    <div class="mt-auto">
                        <a href="#" class="btn w-100" style="background-color: #1abc9c; border-color: #1abc9c; color: white; pointer-events: none; opacity: 0.6;">
                            <i class="fas fa-credit-card me-2"></i>Manage Store Credit
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Locked card styling */
    .card-locked {
        position: relative;
        opacity: 0.7;
        cursor: not-allowed;
    }

    .card-locked .btn {
        pointer-events: none;
        opacity: 0.6;
    }

    .card-locked::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        z-index: 5;
    }
</style>
{% endblock %}
