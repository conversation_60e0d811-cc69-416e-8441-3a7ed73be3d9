{% extends "base.html" %}
{% block title %}Sellers Network Dashboard{% endblock %}
{% block content %}
<style>
    /* Set the background color to match dashboard */
    body {
        background-color: #6b21a8; /* Purple background to match dashboard */
    }

    .main-content {
        background-color: #6b21a8;
    }
</style>
<div class="container mt-5">
    <div class="row">
        <!-- Browse Network Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(52, 152, 219, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-search fa-2x" style="color: #3498db;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Browse Network</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Search and browse inventory from other local game stores in the network. Find products for your customers that you don't have in stock.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('sellers_network.browse_network') }}" class="btn w-100" style="background-color: #3498db; border-color: #3498db; color: white;">
                            <i class="fas fa-search me-2"></i>Browse Inventory
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- My Orders Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(46, 204, 113, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-shopping-bag fa-2x" style="color: #2ecc71;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">My Orders</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">View and manage orders you've placed with other stores in the network. Track order status and delivery information.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('sellers_network.my_orders') }}" class="btn w-100" style="background-color: #2ecc71; border-color: #2ecc71; color: white;">
                            <i class="fas fa-shopping-bag me-2"></i>View My Orders
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Incoming Orders Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(230, 126, 34, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-inbox fa-2x" style="color: #e67e22;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Incoming Orders</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manage orders from other stores who want to purchase from your inventory. Process and fulfill network orders.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('sellers_network.incoming_orders') }}" class="btn w-100" style="background-color: #e67e22; border-color: #e67e22; color: white;">
                            <i class="fas fa-inbox me-2"></i>View Incoming Orders
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Network Settings Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(155, 89, 182, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-cogs fa-2x" style="color: #9b59b6;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Network Settings</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Configure your network preferences, shipping settings, and inventory sharing options for the sellers network.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('sellers_network.network_settings') }}" class="btn w-100" style="background-color: #9b59b6; border-color: #9b59b6; color: white;">
                            <i class="fas fa-cogs me-2"></i>Manage Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Network Analytics Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(231, 76, 60, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-chart-bar fa-2x" style="color: #e74c3c;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Network Analytics</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">View analytics and insights about your network activity, sales performance, and partnership opportunities.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('sellers_network.network_analytics') }}" class="btn w-100" style="background-color: #e74c3c; border-color: #e74c3c; color: white;">
                            <i class="fas fa-chart-bar me-2"></i>View Analytics
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Partner Stores Card -->
        <div class="col-md-6 mb-4">
            <div class="card h-100" style="border-radius: 12px; background-color: rgba(25, 25, 39, 0.8); border: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); overflow: hidden;">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3" style="width: 50px; height: 50px; border-radius: 12px; background-color: rgba(26, 188, 156, 0.2); display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-handshake fa-2x" style="color: #1abc9c;"></i>
                        </div>
                        <h3 class="card-title mb-0 text-white">Partner Stores</h3>
                    </div>
                    <p class="card-text text-white-50 mb-4">Manage your partnerships with other local game stores. View store profiles, ratings, and establish new partnerships.</p>
                    <div class="mt-auto">
                        <a href="{{ url_for('sellers_network.partner_stores') }}" class="btn w-100" style="background-color: #1abc9c; border-color: #1abc9c; color: white;">
                            <i class="fas fa-handshake me-2"></i>Manage Partners
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
